<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>بطاقة الأعمال الذكية</title>
    
    <!-- Font Awesome للأيقونات -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.0.0/css/all.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* إعادة تعيين الأنماط الأساسية */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* دعم Font Awesome */
        .fas, .far, .fab, .fal, .fad {
            font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "FontAwesome" !important;
            font-weight: 900 !important;
            font-style: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 1 !important;
            -webkit-font-smoothing: antialiased !important;
            -moz-osx-font-smoothing: grayscale !important;
        }

        .fab {
            font-weight: 400 !important;
        }

        /* أيقونات Unicode احتياطية */
        .fa-phone:before { content: "\f095"; }
        .fa-envelope:before { content: "\f0e0"; }
        .fa-globe:before { content: "\f0ac"; }
        .fa-camera:before { content: "\f030"; }
        .fa-lightbulb:before { content: "\f0eb"; }
        .fa-whatsapp:before { content: "\f232"; }
        .fa-telegram-plane:before { content: "\f3fe"; }
        .fa-instagram:before { content: "\f16d"; }
        .fa-facebook-f:before { content: "\f39e"; }
        .fa-twitter:before { content: "\f099"; }
        .fa-youtube:before { content: "\f167"; }
        .fa-tiktok:before { content: "\e07b"; }
        .fa-snapchat-ghost:before { content: "\f2ac"; }
        .fa-linkedin-in:before { content: "\f0e1"; }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: hidden;
        }

        /* حاوي الهاتف الرئيسي */
        .phone {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            position: relative;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3), inset 0 0 0 8px #1a1a1a;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        /* الشاشة */
        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 32px;
            position: relative;
            overflow: hidden;
        }

        /* شريط الحالة */
        .status-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 44px;
            background: rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            z-index: 1000;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid white;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: white;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            height: 100%;
            background: #4CAF50;
            border-radius: 1px;
            transition: width 0.3s ease;
        }

        /* شاشة القفل */
        .lock-screen {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 900;
            transition: all 0.5s ease;
        }

        .lock-time {
            font-size: 4em;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 2px 2px 10px rgba(0,0,0,0.3);
        }

        .lock-date {
            font-size: 1.2em;
            opacity: 0.8;
            margin-bottom: 50px;
        }

        /* مؤشر فتح القفل */
        .unlock-indicator {
            position: absolute;
            bottom: 120px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            opacity: 0.8;
        }

        .unlock-text {
            font-size: 16px;
            color: white;
            text-align: center;
        }

        .unlock-arrow {
            width: 60px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            cursor: pointer;
            transition: all 0.3s ease;
            animation: arrowPulse 2s infinite;
        }

        .unlock-arrow:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .unlock-arrow i {
            color: white;
            font-size: 18px;
            animation: arrowMove 2s infinite;
        }

        @keyframes arrowPulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        @keyframes arrowMove {
            0%, 100% { transform: translateX(0); }
            50% { transform: translateX(5px); }
        }

        /* الشاشة الرئيسية */
        .home-screen {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 100px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transform: scale(0.9);
            transition: all 0.5s ease;
        }

        .home-screen.show {
            opacity: 1;
            transform: scale(1);
        }

        /* الشعار المركزي */
        .center-logo {
            width: 120px;
            height: 120px;
            background: rgba(255,255,255,0.1);
            border-radius: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255,255,255,0.2);
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .center-logo:hover {
            transform: scale(1.05);
            background: rgba(255,255,255,0.15);
        }

        .center-logo i {
            font-size: 3em;
            color: white;
            text-shadow: 2px 2px 10px rgba(0,0,0,0.3);
        }

        .company-name {
            color: white;
            font-size: 1.5em;
            font-weight: 600;
            text-align: center;
            text-shadow: 2px 2px 10px rgba(0,0,0,0.3);
        }

        /* حاوي التطبيقات */
        .apps-container {
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            bottom: 100px;
            opacity: 0;
            transform: translateY(30px) scale(0.9);
            transition: all 0.4s ease;
            pointer-events: none;
            z-index: 600;
            padding: 20px;
            overflow: visible;
        }

        .apps-container.show {
            opacity: 1;
            transform: translateY(0) scale(1);
            pointer-events: auto;
        }

        .apps-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            padding: 20px;
            height: 100%;
            align-content: start;
        }

        /* أيقونة التطبيق */
        .app-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8) translateY(20px);
        }

        .app-item.show {
            opacity: 1;
            transform: scale(1) translateY(0);
        }

        .app-item:hover {
            transform: scale(1.1) translateY(-5px);
        }

        .app-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }

        .app-icon i {
            font-size: 2em;
            color: white;
            z-index: 2;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.3);
        }

        .app-name {
            color: white;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
            max-width: 70px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* ألوان التطبيقات */
        .whatsapp { background: linear-gradient(135deg, #25D366, #128C7E); }
        .telegram { background: linear-gradient(135deg, #0088cc, #005577); }
        .instagram { background: linear-gradient(135deg, #E4405F, #C13584, #833AB4); }
        .facebook { background: linear-gradient(135deg, #1877F2, #0C63D4); }
        .twitter { background: linear-gradient(135deg, #1DA1F2, #0C7ABF); }
        .youtube { background: linear-gradient(135deg, #FF0000, #CC0000); }
        .tiktok { background: linear-gradient(135deg, #000000, #333333); }
        .snapchat { background: linear-gradient(135deg, #FFFC00, #CCCA00); }
        .linkedin { background: linear-gradient(135deg, #0077B5, #005885); }
        .phone-app { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .email-app { background: linear-gradient(135deg, #2196F3, #1976D2); }
        .website-app { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .camera-app { background: linear-gradient(135deg, #9C27B0, #7B1FA2); }
        .settings { background: linear-gradient(135deg, #607D8B, #455A64); }

        /* الشريط السفلي */
        .bottom-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 100px;
            background: rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 30px;
            z-index: 800;
        }

        .bottom-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .bottom-icon:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }

        .bottom-icon i {
            color: white;
            font-size: 1.5em;
        }

        /* الزر الدائري المركزي */
        .home-button {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: rgba(255,255,255,0.15);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255,255,255,0.3);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .home-button:hover {
            background: rgba(255,255,255,0.25);
            transform: scale(1.1);
        }

        .home-button:active {
            transform: scale(0.95);
        }

        .home-button i {
            color: white;
            font-size: 1.8em;
        }

        /* الاختصارات الجانبية */
        .camera-shortcut, .flashlight-shortcut {
            position: absolute;
            bottom: 130px;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .camera-shortcut {
            right: 30px;
        }

        .flashlight-shortcut {
            left: 30px;
        }

        .camera-shortcut:hover, .flashlight-shortcut:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.1);
        }

        .camera-shortcut i, .flashlight-shortcut i {
            color: white;
            font-size: 1.2em;
        }

        /* حالة المصباح المضيء */
        .flashlight-shortcut.flashlight-on {
            background: radial-gradient(circle, #FFD700, #FFA500);
            box-shadow: 0 0 20px #FFD700, inset 0 0 20px rgba(255,255,255,0.3);
        }

        .flashlight-shortcut.flashlight-on i {
            color: #000;
        }

        /* التصميم المتجاوب */
        @media (max-width: 480px) {
            .phone {
                width: 90vw;
                height: 90vh;
                max-width: 375px;
                max-height: 812px;
            }
            
            body {
                padding: 10px;
            }
            
            .apps-grid {
                gap: 20px;
                padding: 15px;
            }
            
            .app-icon {
                width: 60px;
                height: 60px;
            }
            
            .app-icon i {
                font-size: 1.8em;
            }
            
            .lock-time {
                font-size: 3em;
            }
            
            .center-logo {
                width: 100px;
                height: 100px;
            }
            
            .center-logo i {
                font-size: 2.5em;
            }
        }

        @media (max-width: 320px) {
            .apps-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }
            
            .app-icon {
                width: 55px;
                height: 55px;
            }
            
            .app-icon i {
                font-size: 1.6em;
            }
            
            .app-name {
                font-size: 11px;
            }
        }

        /* أنيميشن الجسيمات */
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.6);
            border-radius: 50%;
            pointer-events: none;
            animation: float 10s linear infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* إشعارات */
        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 16px;
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                transform: translateX(-50%) translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                transform: translateX(-50%) translateY(0);
                opacity: 1;
            }
            to {
                transform: translateX(-50%) translateY(-100%);
                opacity: 0;
            }
        }

        /* حالة القفل */
        .phone.locked .home-screen {
            opacity: 0;
            transform: scale(0.9);
        }

        .phone.locked .apps-container {
            opacity: 0;
            transform: translateY(30px) scale(0.9);
            pointer-events: none;
        }

        /* تأثيرات الضغط */
        .pressable {
            transition: transform 0.1s ease;
        }

        .pressable:active {
            transform: scale(0.95);
        }

        /* تحسينات الأداء */
        .phone, .screen, .apps-container, .app-item {
            will-change: transform, opacity;
        }

        /* تأثيرات التمرير */
        .smooth-scroll {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <div class="phone" id="phone">
        <div class="screen">
            <!-- شريط الحالة -->
            <div class="status-bar">
                <div class="status-left">
                    <span id="currentTime">12:34</span>
                </div>
                <div class="status-right">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <div class="battery">
                        <div class="battery-fill" id="batteryFill" style="width: 85%;"></div>
                    </div>
                    <span id="batteryPercent">85%</span>
                </div>
            </div>

            <!-- شاشة القفل -->
            <div class="lock-screen" id="lockScreen">
                <div class="lock-time" id="lockTime">12:34</div>
                <div class="lock-date" id="lockDate">الجمعة، 9 أغسطس</div>
                
                <div class="unlock-indicator" id="unlockIndicator">
                    <div class="unlock-text">اسحب لإظهار التطبيقات</div>
                    <div class="unlock-arrow pressable" id="unlockArrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>

            <!-- الشاشة الرئيسية -->
            <div class="home-screen" id="homeScreen">
                <div class="center-logo pressable" id="centerLogo">
                    <i class="fas fa-building"></i>
                </div>
                <div class="company-name">شركة التقنية المتقدمة</div>
            </div>

            <!-- حاوي التطبيقات -->
            <div class="apps-container" id="appsContainer">
                <div class="apps-grid" id="appsGrid">
                    <!-- التطبيقات ستُضاف ديناميكياً -->
                </div>
            </div>

            <!-- الشريط السفلي -->
            <div class="bottom-bar">
                <div class="bottom-icon pressable" id="phoneIcon">
                    <i class="fas fa-phone"></i>
                </div>
                <div class="bottom-icon pressable" id="messageIcon">
                    <i class="fas fa-comment"></i>
                </div>
                
                <!-- الزر الدائري المركزي -->
                <div class="home-button pressable" id="homeButton">
                    <i class="fas fa-circle"></i>
                </div>
                
                <div class="bottom-icon pressable" id="cameraIcon">
                    <i class="fas fa-camera"></i>
                </div>
                <div class="bottom-icon pressable" id="settingsIcon">
                    <i class="fas fa-cog"></i>
                </div>
            </div>

            <!-- الاختصارات الجانبية -->
            <div class="camera-shortcut pressable" id="cameraShortcut">
                <i class="fas fa-camera"></i>
            </div>
            <div class="flashlight-shortcut pressable" id="flashlightShortcut">
                <i class="fas fa-lightbulb"></i>
            </div>
        </div>
    </div>

    <script>
        // المتغيرات العامة
        let isLocked = true;
        let batteryLevel = Math.floor(Math.random() * 30) + 70;
        let currentPage = 1;
        const totalPages = 2;

        // بيانات التطبيقات
        const appsData = [
            { name: 'واتساب', icon: 'fab fa-whatsapp', url: 'https://wa.me/966501234567', class: 'whatsapp' },
            { name: 'تيليجرام', icon: 'fab fa-telegram-plane', url: 'https://t.me/username', class: 'telegram' },
            { name: 'انستغرام', icon: 'fab fa-instagram', url: 'https://instagram.com/username', class: 'instagram' },
            { name: 'فيسبوك', icon: 'fab fa-facebook-f', url: 'https://facebook.com/username', class: 'facebook' },
            { name: 'تويتر', icon: 'fab fa-twitter', url: 'https://twitter.com/username', class: 'twitter' },
            { name: 'يوتيوب', icon: 'fab fa-youtube', url: 'https://youtube.com/@username', class: 'youtube' },
            { name: 'تيك توك', icon: 'fab fa-tiktok', url: 'https://tiktok.com/@username', class: 'tiktok' },
            { name: 'سناب شات', icon: 'fab fa-snapchat-ghost', url: 'https://snapchat.com/add/username', class: 'snapchat' },
            { name: 'لينكد إن', icon: 'fab fa-linkedin-in', url: 'https://linkedin.com/in/username', class: 'linkedin' },
            { name: 'الهاتف', icon: 'fas fa-phone', url: 'tel:+966501234567', class: 'phone-app' },
            { name: 'البريد', icon: 'fas fa-envelope', url: 'mailto:<EMAIL>', class: 'email-app' },
            { name: 'الموقع', icon: 'fas fa-globe', url: 'https://company.com', class: 'website-app' }
        ];

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تهيئة بطاقة الأعمال الذكية...');
            
            initializeTime();
            initializeBattery();
            createApps();
            setupEventListeners();
            initializeParticles();
            
            console.log('✅ تم تحميل التطبيق بنجاح');
        });

        // تهيئة الوقت والتاريخ
        function initializeTime() {
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA', { 
                    hour: '2-digit', 
                    minute: '2-digit',
                    hour12: false 
                });
                const dateString = now.toLocaleDateString('ar-SA', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });

                document.getElementById('currentTime').textContent = timeString;
                document.getElementById('lockTime').textContent = timeString;
                document.getElementById('lockDate').textContent = dateString;
            }

            updateTime();
            setInterval(updateTime, 1000);
        }

        // تهيئة البطارية
        function initializeBattery() {
            function updateBattery() {
                // محاكاة تغيير البطارية
                batteryLevel = Math.max(20, batteryLevel + (Math.random() - 0.5) * 2);
                
                const batteryFill = document.getElementById('batteryFill');
                const batteryPercent = document.getElementById('batteryPercent');
                
                batteryFill.style.width = batteryLevel + '%';
                batteryPercent.textContent = Math.round(batteryLevel) + '%';
                
                // تغيير لون البطارية حسب المستوى
                if (batteryLevel < 20) {
                    batteryFill.style.background = '#f44336';
                } else if (batteryLevel < 50) {
                    batteryFill.style.background = '#ff9800';
                } else {
                    batteryFill.style.background = '#4CAF50';
                }
            }

            updateBattery();
            setInterval(updateBattery, 30000); // كل 30 ثانية
        }

        // إنشاء التطبيقات
        function createApps() {
            const appsGrid = document.getElementById('appsGrid');
            appsGrid.innerHTML = '';

            appsData.forEach((app, index) => {
                const appItem = document.createElement('div');
                appItem.className = 'app-item';
                appItem.setAttribute('data-url', app.url);

                appItem.innerHTML = `
                    <div class="app-icon ${app.class}">
                        <i class="${app.icon}"></i>
                    </div>
                    <div class="app-name">${app.name}</div>
                `;

                // إضافة حدث النقر
                appItem.addEventListener('click', function() {
                    const url = this.getAttribute('data-url');
                    if (url && url !== '#') {
                        playClickSound();
                        showNotification(`فتح ${app.name}`, app.icon.includes('fab') ? '📱' : '🔗');
                        setTimeout(() => {
                            window.open(url, '_blank');
                        }, 300);
                    }
                });

                appsGrid.appendChild(appItem);
            });

            console.log(`✅ تم إنشاء ${appsData.length} تطبيق`);
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            const unlockArrow = document.getElementById('unlockArrow');
            const homeButton = document.getElementById('homeButton');
            const cameraShortcut = document.getElementById('cameraShortcut');
            const flashlightShortcut = document.getElementById('flashlightShortcut');
            const centerLogo = document.getElementById('centerLogo');

            // فتح القفل
            unlockArrow.addEventListener('click', unlockPhone);

            // الزر الدائري المركزي
            homeButton.addEventListener('click', handleHomeButton);

            // الكاميرا
            cameraShortcut.addEventListener('click', function(e) {
                e.stopPropagation();
                openCamera();
            });

            // المصباح
            flashlightShortcut.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleFlashlight();
            });

            // الشعار المركزي
            centerLogo.addEventListener('click', function() {
                showApps();
            });

            // أحداث اللمس للهواتف
            setupTouchEvents();

            // أحداث الأزرار السفلية
            setupBottomBarEvents();

            console.log('✅ تم إعداد مستمعي الأحداث');
        }

        // إعداد أحداث اللمس
        function setupTouchEvents() {
            let startX = 0;
            let startY = 0;
            let isPressed = false;

            const unlockArrow = document.getElementById('unlockArrow');

            // بداية اللمس
            unlockArrow.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                isPressed = true;
                this.style.transform = 'scale(0.95)';
            });

            // حركة اللمس
            unlockArrow.addEventListener('touchmove', function(e) {
                if (!isPressed) return;

                const currentX = e.touches[0].clientX;
                const currentY = e.touches[0].clientY;
                const deltaX = currentX - startX;
                const deltaY = currentY - startY;

                // إذا كانت الحركة أفقية أكثر من العمودية
                if (Math.abs(deltaX) > Math.abs(deltaY) && deltaX > 50) {
                    unlockPhone();
                    isPressed = false;
                }
            });

            // نهاية اللمس
            unlockArrow.addEventListener('touchend', function(e) {
                isPressed = false;
                this.style.transform = 'scale(1)';
            });
        }

        // إعداد أحداث الشريط السفلي
        function setupBottomBarEvents() {
            document.getElementById('phoneIcon').addEventListener('click', function() {
                playClickSound();
                showNotification('فتح تطبيق الهاتف', '📞');
                setTimeout(() => window.open('tel:+966501234567', '_self'), 300);
            });

            document.getElementById('messageIcon').addEventListener('click', function() {
                playClickSound();
                showNotification('فتح الرسائل', '💬');
                setTimeout(() => window.open('sms:+966501234567', '_self'), 300);
            });

            document.getElementById('cameraIcon').addEventListener('click', function() {
                openCamera();
            });

            document.getElementById('settingsIcon').addEventListener('click', function() {
                playClickSound();
                showNotification('فتح الإعدادات', '⚙️');
            });
        }

        // فتح القفل
        function unlockPhone() {
            console.log('🔓 فتح القفل...');

            const lockScreen = document.getElementById('lockScreen');
            const homeScreen = document.getElementById('homeScreen');
            const phone = document.getElementById('phone');

            isLocked = false;
            phone.classList.remove('locked');

            // إخفاء شاشة القفل
            lockScreen.style.opacity = '0';
            lockScreen.style.transform = 'scale(1.1)';

            setTimeout(() => {
                lockScreen.style.display = 'none';
                homeScreen.classList.add('show');
                playClickSound();
                showNotification('تم فتح القفل بنجاح', '🔓');
            }, 300);

            // اهتزاز للهواتف
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

            console.log('✅ تم فتح القفل');
        }

        // التعامل مع الزر الدائري
        function handleHomeButton() {
            console.log('🏠 الضغط على الزر الدائري');

            if (isLocked) {
                unlockPhone();
                return;
            }

            const appsContainer = document.getElementById('appsContainer');
            const centerLogo = document.getElementById('centerLogo');
            const homeScreen = document.getElementById('homeScreen');

            const appsVisible = appsContainer.classList.contains('show');

            if (appsVisible) {
                // إخفاء التطبيقات وإظهار الشاشة الرئيسية
                hideApps();
                setTimeout(() => {
                    homeScreen.classList.add('show');
                }, 300);
            } else {
                // إظهار التطبيقات
                showApps();
            }

            playClickSound();
        }

        // إظهار التطبيقات
        function showApps() {
            console.log('📱 إظهار التطبيقات...');

            const appsContainer = document.getElementById('appsContainer');
            const homeScreen = document.getElementById('homeScreen');
            const appItems = document.querySelectorAll('.app-item');

            // إخفاء الشاشة الرئيسية
            homeScreen.classList.remove('show');

            setTimeout(() => {
                // إظهار حاوي التطبيقات
                appsContainer.classList.add('show');

                // إظهار التطبيقات بتأثير متدرج
                appItems.forEach((app, index) => {
                    setTimeout(() => {
                        app.classList.add('show');
                    }, index * 100);
                });

                playClickSound();
                showNotification('عرض التطبيقات', '📱');
            }, 200);

            console.log('✅ تم إظهار التطبيقات');
        }

        // إخفاء التطبيقات
        function hideApps() {
            console.log('📱 إخفاء التطبيقات...');

            const appsContainer = document.getElementById('appsContainer');
            const appItems = document.querySelectorAll('.app-item');

            // إخفاء التطبيقات بتأثير متدرج
            appItems.forEach((app, index) => {
                setTimeout(() => {
                    app.classList.remove('show');
                }, index * 50);
            });

            setTimeout(() => {
                appsContainer.classList.remove('show');
                playClickSound();
            }, 300);

            console.log('✅ تم إخفاء التطبيقات');
        }

        // فتح الكاميرا
        function openCamera() {
            console.log('📷 فتح الكاميرا...');
            playClickSound();

            // تأثير بصري
            const cameraShortcut = document.getElementById('cameraShortcut');
            cameraShortcut.style.transform = 'scale(0.9)';
            setTimeout(() => {
                cameraShortcut.style.transform = 'scale(1)';
            }, 150);

            // التحقق من دعم الكاميرا
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                showNotification('الكاميرا غير مدعومة في هذا المتصفح', '❌');
                return;
            }

            // طلب الوصول للكاميرا
            navigator.mediaDevices.getUserMedia({
                video: {
                    width: { ideal: 1280 },
                    height: { ideal: 720 },
                    facingMode: 'user'
                }
            })
            .then(function(stream) {
                console.log('✅ تم الحصول على إذن الكاميرا');

                // إنشاء نافذة الكاميرا
                const cameraWindow = window.open('', '_blank', 'width=800,height=600,resizable=yes');

                if (!cameraWindow) {
                    showNotification('يرجى السماح بفتح النوافذ المنبثقة', '⚠️');
                    stream.getTracks().forEach(track => track.stop());
                    return;
                }

                createCameraInterface(cameraWindow, stream);
                showNotification('تم فتح الكاميرا بنجاح', '📷');
            })
            .catch(function(error) {
                console.error('❌ خطأ في الكاميرا:', error);

                let message = 'لا يمكن الوصول للكاميرا. ';
                if (error.name === 'NotAllowedError') {
                    message += 'يرجى السماح بالوصول للكاميرا.';
                } else if (error.name === 'NotFoundError') {
                    message += 'لم يتم العثور على كاميرا.';
                } else {
                    message += 'تأكد من أن الكاميرا غير مستخدمة.';
                }

                showNotification(message, '❌');
            });
        }

        // إنشاء واجهة الكاميرا
        function createCameraInterface(cameraWindow, stream) {
            cameraWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>الكاميرا الذكية</title>
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body {
                            background: #000;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            font-family: 'Cairo', Arial, sans-serif;
                            overflow: hidden;
                        }
                        video {
                            max-width: 90%;
                            max-height: 70vh;
                            border-radius: 15px;
                            box-shadow: 0 10px 30px rgba(255,255,255,0.1);
                        }
                        .controls {
                            margin-top: 20px;
                            display: flex;
                            gap: 15px;
                            flex-wrap: wrap;
                            justify-content: center;
                        }
                        button {
                            padding: 12px 24px;
                            background: linear-gradient(135deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            border-radius: 25px;
                            cursor: pointer;
                            font-size: 16px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                        }
                        button:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
                        }
                        .capture {
                            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                            font-size: 18px;
                            padding: 15px 30px;
                        }
                        .switch { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
                        .close { background: linear-gradient(135deg, #95a5a6, #7f8c8d); }
                        .title {
                            color: white;
                            font-size: 24px;
                            font-weight: 700;
                            margin-bottom: 20px;
                            text-align: center;
                        }
                    </style>
                </head>
                <body>
                    <div class="title">📷 الكاميرا الذكية</div>
                    <video id="video" autoplay playsinline></video>
                    <div class="controls">
                        <button class="capture" onclick="takePhoto()">📸 التقاط صورة</button>
                        <button class="switch" onclick="switchCamera()">🔄 تبديل الكاميرا</button>
                        <button class="close" onclick="closeCamera()">❌ إغلاق</button>
                    </div>

                    <script>
                        let currentStream = null;
                        let facingMode = 'user';

                        function initCamera(stream) {
                            const video = document.getElementById('video');
                            currentStream = stream;
                            video.srcObject = stream;
                        }

                        function takePhoto() {
                            const video = document.getElementById('video');
                            const canvas = document.createElement('canvas');
                            canvas.width = video.videoWidth;
                            canvas.height = video.videoHeight;

                            const ctx = canvas.getContext('2d');
                            ctx.drawImage(video, 0, 0);

                            // تحميل الصورة
                            const link = document.createElement('a');
                            link.download = 'smart_photo_' + new Date().getTime() + '.png';
                            link.href = canvas.toDataURL('image/png');
                            link.click();

                            // تأثير فلاش
                            document.body.style.background = '#fff';
                            setTimeout(() => {
                                document.body.style.background = '#000';
                            }, 100);
                        }

                        function switchCamera() {
                            facingMode = facingMode === 'user' ? 'environment' : 'user';

                            if (currentStream) {
                                currentStream.getTracks().forEach(track => track.stop());
                            }

                            navigator.mediaDevices.getUserMedia({
                                video: {
                                    facingMode: facingMode,
                                    width: { ideal: 1280 },
                                    height: { ideal: 720 }
                                }
                            })
                            .then(initCamera)
                            .catch(err => {
                                console.error('خطأ في تبديل الكاميرا:', err);
                                alert('لا يمكن تبديل الكاميرا');
                            });
                        }

                        function closeCamera() {
                            if (currentStream) {
                                currentStream.getTracks().forEach(track => track.stop());
                            }
                            window.close();
                        }

                        // تهيئة الكاميرا
                        window.addEventListener('load', function() {
                            initCamera(arguments[0]);
                        });

                        window.addEventListener('beforeunload', closeCamera);
                    </script>
                </body>
                </html>
            `);

            // تمرير stream للنافذة الجديدة
            cameraWindow.addEventListener('load', function() {
                cameraWindow.initCamera(stream);
            });
        }

        // تبديل المصباح
        function toggleFlashlight() {
            console.log('🔦 تبديل المصباح...');
            playClickSound();

            const flashlightShortcut = document.getElementById('flashlightShortcut');
            const isOn = flashlightShortcut.classList.contains('flashlight-on');

            // تأثير بصري
            flashlightShortcut.style.transform = 'scale(0.9)';
            setTimeout(() => {
                flashlightShortcut.style.transform = 'scale(1)';
            }, 150);

            if (isOn) {
                // إطفاء المصباح
                flashlightShortcut.classList.remove('flashlight-on');
                showNotification('تم إطفاء المصباح', '🔦');
                console.log('🔦 تم إطفاء المصباح');
            } else {
                // تشغيل المصباح
                flashlightShortcut.classList.add('flashlight-on');
                showNotification('تم تشغيل المصباح', '💡');
                console.log('💡 تم تشغيل المصباح');

                // إطفاء تلقائي بعد 30 ثانية
                setTimeout(() => {
                    if (flashlightShortcut.classList.contains('flashlight-on')) {
                        toggleFlashlight();
                    }
                }, 30000);
            }

            // اهتزاز للهواتف
            if (navigator.vibrate) {
                navigator.vibrate(100);
            }
        }

        // تشغيل صوت النقر
        function playClickSound() {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (error) {
                console.log('لا يمكن تشغيل الصوت:', error);
            }
        }

        // إظهار الإشعارات
        function showNotification(message, icon) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.innerHTML = `<span style="font-size: 20px;">${icon}</span> ${message}`;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideUp 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // تهيئة الجسيمات المتحركة
        function initializeParticles() {
            function createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDuration = (Math.random() * 10 + 5) + 's';
                particle.style.animationDelay = Math.random() * 5 + 's';

                document.body.appendChild(particle);

                // إزالة الجسيم بعد انتهاء الأنيميشن
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 15000);
            }

            // إنشاء جسيم كل 3 ثوان
            setInterval(createParticle, 3000);

            // إنشاء بعض الجسيمات في البداية
            for (let i = 0; i < 5; i++) {
                setTimeout(createParticle, i * 1000);
            }
        }

        // دعم لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'Enter':
                case ' ':
                    if (isLocked) {
                        unlockPhone();
                    } else {
                        handleHomeButton();
                    }
                    break;
                case 'c':
                case 'C':
                    openCamera();
                    break;
                case 'f':
                case 'F':
                    toggleFlashlight();
                    break;
                case 'Escape':
                    if (!isLocked) {
                        hideApps();
                    }
                    break;
            }
        });

        console.log('🎯 تم تحميل جميع الوظائف بنجاح');
    </script>
</body>
</html>
