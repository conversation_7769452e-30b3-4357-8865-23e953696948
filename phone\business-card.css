/* Font Awesome Fallback */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* Ensure Font Awesome icons are visible */
.fas, .far, .fab, .fal, .fad {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

.fab {
    font-weight: 400 !important;
}

/* Force icon visibility */
i[class*="fa-"] {
    display: inline-block !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    -webkit-font-smoothing: antialiased !important;
}

/* Specific icon fixes */
.fa-phone:before { content: "\f095"; }
.fa-comment:before { content: "\f075"; }
.fa-camera:before { content: "\f030"; }
.fa-lightbulb:before { content: "\f0eb"; }
.fa-chevron-right:before { content: "\f054"; }
.fa-whatsapp:before { content: "\f232"; }
.fa-telegram-plane:before { content: "\f3fe"; }
.fa-instagram:before { content: "\f16d"; }
.fa-facebook-f:before { content: "\f39e"; }
.fa-twitter:before { content: "\f099"; }
.fa-youtube:before { content: "\f167"; }
.fa-tiktok:before { content: "\e07b"; }
.fa-snapchat-ghost:before { content: "\f2ac"; }
.fa-linkedin-in:before { content: "\f0e1"; }

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}



body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    transition: all 0.3s ease;
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    will-change: transform;
}

/* Dark Theme - Black and Gold */
.dark-theme {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #**********%) !important;
}

.dark-theme .main-container {
    background: rgba(0, 0, 0, 0.1);
}

.dark-theme .company-section {
    background: transparent;
    box-shadow: none;
}

.dark-theme .company-name {
    color: #E8E8E8;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.dark-theme .company-slogan {
    color: #B8B8B8;
}

.dark-theme .company-logo {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(74, 144, 226, 0.4);
}

.dark-theme .qr-section {
    background: transparent;
    box-shadow: none;
}

.dark-theme .qr-code {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
    box-shadow: 0 10px 30px rgba(74, 144, 226, 0.4);
}

.dark-theme .qr-text {
    color: #E8E8E8;
}

.dark-theme .contact-info {
    color: #B8B8B8;
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.theme-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.theme-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.dark-theme .theme-btn {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000;
    border: 2px solid #FFD700;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.dark-theme .theme-btn:hover {
    background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);
}

/* Background Effects */
.background-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.background-waves {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.wave {
    position: absolute;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 50%;
    animation: wave 20s linear infinite;
}

.wave1 {
    top: -50%;
    left: -50%;
    animation-delay: 0s;
}

.wave2 {
    top: -60%;
    left: -60%;
    animation-delay: -5s;
}

.wave3 {
    top: -70%;
    left: -70%;
    animation-delay: -10s;
}

@keyframes wave {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Main Container */
.main-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 60px;
    max-width: 1400px;
    width: 100%;
    margin: 0 auto;
    padding: 40px 20px;
    min-height: 100vh;
    position: relative;
}

/* Company Section */
.company-section {
    flex: 1;
    max-width: 400px;
    text-align: center;
    color: white;
}

.company-logo {
    width: 180px;
    height: 180px;
    background: linear-gradient(145deg, #ffffff 0%, #f0f0f0 50%, #e0e0e0 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 80px;
    color: #667eea;
    margin: 0 auto 30px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 10px 30px rgba(0, 0, 0, 0.2),
        inset 0 4px 8px rgba(255, 255, 255, 0.8),
        inset 0 -4px 8px rgba(0, 0, 0, 0.1);
    animation: logoFloat 4s ease-in-out infinite;
    transition: all 0.3s ease;
}

.company-logo:hover {
    transform: scale(1.05) rotateY(10deg);
}

@keyframes logoFloat {
    0%, 100% { 
        transform: translateY(0px) rotateX(0deg);
    }
    50% { 
        transform: translateY(-15px) rotateX(5deg);
    }
}

.company-name {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.company-slogan {
    font-size: 18px;
    opacity: 0.9;
    margin-bottom: 40px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Company Stats */
.company-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 14px;
    opacity: 0.8;
    margin-top: 5px;
}

/* Phone Container */
.phone-container {
    position: relative;
    z-index: 10;
}

/* iPhone Frame with 3D Effects */
.phone {
    width: 330px;
    height: 600px;
    background: linear-gradient(145deg, #2c2c2e 0%, #1c1c1e 25%, #000000 50%, #1c1c1e 75%, #2c2c2e 100%);
    border-radius: 50px;
    padding: 6px;
    position: relative;
    transform-style: preserve-3d;
    transform: perspective(1200px) rotateX(8deg) rotateY(-8deg);
    box-shadow:
        0 40px 100px rgba(0, 0, 0, 0.7),
        0 25px 60px rgba(0, 0, 0, 0.5),
        0 15px 35px rgba(0, 0, 0, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.3),
        inset 0 3px 6px rgba(255, 255, 255, 0.15),
        inset 0 -3px 6px rgba(0, 0, 0, 0.4);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: phoneFloat 8s ease-in-out infinite;
    border: 3px solid rgba(255, 255, 255, 0.1);
}

/* Dark Theme Phone Color (Keep current titanium color) */
.dark-theme .phone {
    background: linear-gradient(145deg, #1a1a1a 0%, #000000 25%, #0a0a0a 50%, #000000 75%, #1a1a1a 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    box-shadow:
        0 50px 120px rgba(255, 215, 0, 0.2),
        0 30px 70px rgba(0, 0, 0, 0.8),
        0 20px 40px rgba(255, 215, 0, 0.1),
        0 10px 20px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(255, 215, 0, 0.15),
        inset 0 -2px 4px rgba(0, 0, 0, 0.7),
        inset 0 0 0 1px rgba(255, 215, 0, 0.1);
}

/* Light Theme Phone Color (Titanium) */
.light-theme .phone {
    background: linear-gradient(145deg, #878684 0%, #6a6865 25%, #5a5855 50%, #4a4845 75%, #6a6865 100%);
    border: 3px solid rgba(0, 0, 0, 0.1);
    box-shadow:
        0 40px 100px rgba(0, 0, 0, 0.4),
        0 25px 60px rgba(0, 0, 0, 0.3),
        0 15px 35px rgba(0, 0, 0, 0.2),
        0 5px 15px rgba(0, 0, 0, 0.15),
        inset 0 3px 6px rgba(255, 255, 255, 0.4),
        inset 0 -3px 6px rgba(0, 0, 0, 0.2);
}

.phone:hover {
    transform: perspective(1200px) rotateX(0deg) rotateY(0deg) scale(1.03);
    box-shadow:
        0 60px 150px rgba(0, 0, 0, 0.8),
        0 35px 80px rgba(0, 0, 0, 0.6),
        0 20px 45px rgba(0, 0, 0, 0.5),
        0 10px 25px rgba(0, 0, 0, 0.4),
        inset 0 4px 8px rgba(255, 255, 255, 0.2),
        inset 0 -4px 8px rgba(0, 0, 0, 0.5);
    border: 3px solid rgba(255, 255, 255, 0.15);
}

@keyframes phoneFloat {
    0%, 100% { 
        transform: perspective(1000px) rotateX(5deg) rotateY(-5deg) translateY(0px);
    }
    50% { 
        transform: perspective(1000px) rotateX(5deg) rotateY(-5deg) translateY(-10px);
    }
}

.phone-frame {
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #000000 0%, #1a1a1a 25%, #000000 50%, #0a0a0a 75%, #**********%);
    border-radius: 44px;
    overflow: hidden;
    position: relative;
    box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.08),
        inset 0 -2px 4px rgba(0, 0, 0, 0.6),
        inset 0 0 15px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.03);
}

/* Status Bar */
.status-bar {
    height: 44px;
    background: rgba(0, 0, 0, 0.95);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(20px);
    position: relative;
    border-radius: 44px 44px 0 0;
}

.dark-theme .status-bar {
    background: rgba(0, 0, 0, 0.95);
    color: #FFD700;
    border-bottom: 1px solid #FFD700;
}

.status-center {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 100%;
    display: flex;
    align-items: center;
}

.notch {
    width: 150px;
    height: 30px;
    background: #000;
    border-radius: 0 0 20px 20px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.camera {
    width: 8px;
    height: 8px;
    background: #333;
    border-radius: 50%;
    border: 1px solid #555;
}

.speaker {
    width: 40px;
    height: 4px;
    background: #333;
    border-radius: 2px;
    border: 1px solid #555;
}

/* Signal Bars - Removed from notch, only in status-icons now */

.signal-bar {
    width: 3px;
    background: white;
    border-radius: 1px;
    transition: all 0.3s ease;
    animation: signalPulse 2s ease-in-out infinite;
}

.signal-bar1 {
    height: 3px;
    animation-delay: 0s;
}
.signal-bar2 {
    height: 6px;
    animation-delay: 0.2s;
}
.signal-bar3 {
    height: 9px;
    animation-delay: 0.4s;
}
.signal-bar4 {
    height: 12px;
    animation-delay: 0.6s;
}

@keyframes signalPulse {
    0%, 100% {
        opacity: 0.6;
        transform: scaleY(0.8);
    }
    50% {
        opacity: 1;
        transform: scaleY(1.2);
    }
}

/* Status Icons */
.status-icons {
    display: flex;
    align-items: center;
    gap: 8px;
    padding-right: 5px;
}

.network-signal {
    display: flex;
    gap: 1px;
    align-items: flex-end;
    height: 12px;
}

.wifi-icon {
    font-size: 14px;
    opacity: 0.9;
    position: relative;
    display: inline-block;
}

.wifi-icon {
    animation: wifiPulse 3s ease-in-out infinite;
}

.wifi-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    width: calc(100% + 4px);
    height: calc(100% + 4px);
    background: radial-gradient(circle,
        rgba(255, 255, 255, 0.3) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%);
    animation: wifiWaves 2s ease-in-out infinite;
    border-radius: 50%;
    z-index: -1;
}

@keyframes wifiPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.9;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes wifiWaves {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

.battery-container {
    display: flex;
    align-items: center;
    gap: 3px;
}

.battery-icon {
    position: relative;
    width: 20px;
    height: 10px;
    border: 1px solid rgba(255, 255, 255, 0.9);
    border-radius: 2px;
    background: rgba(0, 0, 0, 0.6);
    box-shadow:
        0 0 0 1px rgba(0, 0, 0, 0.2),
        inset 0 1px 1px rgba(0, 0, 0, 0.3);
}

.battery-level {
    position: absolute;
    top: 1px;
    left: 1px;
    height: 6px;
    background: linear-gradient(90deg, #34c759 0%, #30d158 100%);
    border-radius: 1px;
    transition: all 0.4s ease;
    width: 85%;
    box-shadow:
        0 0 2px rgba(52, 199, 89, 0.4),
        inset 0 1px 1px rgba(255, 255, 255, 0.2);
}

.battery-level.low {
    background: linear-gradient(90deg, #ff9500 0%, #ffb340 100%);
}

.battery-level.critical {
    background: linear-gradient(90deg, #ff3b30 0%, #ff6961 100%);
    animation: batteryBlink 1s ease-in-out infinite alternate;
}

@keyframes batteryBlink {
    0% { opacity: 1; }
    100% { opacity: 0.6; }
}

.battery-tip {
    position: absolute;
    right: -3px;
    top: 2px;
    width: 2px;
    height: 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 0 1px 1px 0;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
}

.battery-percentage {
    font-size: 10px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.6);
    background: rgba(0, 0, 0, 0.3);
    padding: 1px 4px;
    border-radius: 4px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-width: 24px;
    text-align: center;
}

/* Date Bar */
.date-bar {
    height: 30px;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 13px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.dark-theme .date-bar {
    background: rgba(0, 0, 0, 0.9);
    color: #FFD700;
    border-bottom: 1px solid #FFD700;
}

/* Screen */
.screen {
    width: 100%;
    height: calc(100% - 74px);
    position: relative;
    overflow: visible;
    border-radius: 25px;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.5),
        inset 0 2px 4px rgba(255, 255, 255, 0.1),
        0 0 30px rgba(30, 60, 114, 0.3);
    transform-style: preserve-3d;
}

/* Wallpaper */
.wallpaper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    filter: blur(0px);
    overflow: visible;
    z-index: 1;
}

.dark-theme .wallpaper {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #FFD700 50%, #1a1a1a 75%, #**********%);
}

/* iOS Style Gradient Orbs */
.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation: float 20s ease-in-out infinite;
    opacity: 0.6;
    z-index: 2;
}

.orb1 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb2 {
    width: 150px;
    height: 150px;
    background: linear-gradient(45deg, #48cae4, #023e8a);
    top: 60%;
    right: 15%;
    animation-delay: -7s;
}

.orb3 {
    width: 180px;
    height: 180px;
    background: linear-gradient(45deg, #a8e6cf, #3d5a80);
    bottom: 20%;
    left: 20%;
    animation-delay: -14s;
}

.dark-theme .orb1 {
    background: linear-gradient(45deg, #FFD700, #FFA500);
}

.dark-theme .orb2 {
    background: linear-gradient(45deg, #FFD700, #FF8C00);
}

.dark-theme .orb3 {
    background: linear-gradient(45deg, #FFA500, #FFD700);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
    }
    33% {
        transform: translateY(-30px) translateX(20px) scale(1.1);
    }
    66% {
        transform: translateY(20px) translateX(-15px) scale(0.9);
    }
}

/* Lock Screen */
.lock-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px 20px 30px;
    color: white;
    text-align: center;
    backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
}

.lock-time {
    margin-top: 60px;
}

.lock-hour {
    font-size: 48px;
    font-weight: 300;
    line-height: 1;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    margin-bottom: 5px;
}

.lock-date {
    font-size: 18px;
    font-weight: 400;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.dark-theme .lock-hour {
    color: #FFD700;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5), 0 0 20px rgba(255, 215, 0, 0.3);
}

.dark-theme .lock-date {
    color: #FFF8DC;
    text-shadow: 0 1px 5px rgba(255, 215, 0, 0.3);
}

/* Lock Screen Widgets */
.lock-widgets {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 15px;
    align-items: center;
}

.widget {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 8px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Battery Widget */
.battery-widget {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    font-weight: 600;
}

.battery-container {
    width: 24px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 2px;
    position: relative;
    background: rgba(0, 0, 0, 0.3);
}

.battery-level {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50 0%, #8BC34A 100%);
    border-radius: 1px;
    transition: all 0.3s ease;
    width: 85%;
}

.battery-level.low {
    background: linear-gradient(90deg, #FF5722 0%, #FF9800 100%);
}

.battery-level.critical {
    background: linear-gradient(90deg, #F44336 0%, #E91E63 100%);
}

.battery-tip {
    position: absolute;
    right: -4px;
    top: 3px;
    width: 2px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0 1px 1px 0;
}

.battery-percentage {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.9);
}

/* Weather Widget */
.weather-widget {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    justify-content: center;
    align-items: center;
    gap: 6px;
}

.weather-widget i {
    font-size: 16px;
    color: #FFD700;
}

.weather-temp {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 700;
}

.weather-location {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
}

/* Lock Bottom */
.lock-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    position: absolute;
    bottom: 15px;
    left: 20px;
    right: 20px;
}

.camera-shortcut,
.flashlight-shortcut {
    width: 50px;
    height: 50px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
    backdrop-filter: blur(30px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 20px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 5px 15px rgba(0, 0, 0, 0.2),
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.1);
    transform-style: preserve-3d;
}

.spacer {
    flex: 1;
}

.camera-shortcut:hover,
.flashlight-shortcut:hover {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.2) 100%);
    transform: scale(1.2) translateY(-5px);
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.4),
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 3px 6px rgba(255, 255, 255, 0.4),
        inset 0 -3px 6px rgba(0, 0, 0, 0.15);
    border: 3px solid rgba(255, 255, 255, 0.6);
}

.camera-shortcut:active,
.flashlight-shortcut:active {
    transform: scale(1.05);
}

.unlock-text {
    font-size: 14px;
    opacity: 0.8;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.4; }
}

/* Center Logo */
.center-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    opacity: 0;
    transition: all 0.5s ease;
    z-index: 5;
}

.center-logo.show {
    opacity: 1;
}

.logo-container {
    width: 120px;
    height: 120px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60px;
    margin: 0 auto 15px;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 10px 30px rgba(0, 0, 0, 0.2),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
    animation: logoFloat 4s ease-in-out infinite;
}

.logo-text {
    font-size: 18px;
    font-weight: 600;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    margin-top: 10px;
}

.dark-theme .logo-container {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0.1) 100%);
    border: 2px solid #FFD700;
    box-shadow:
        0 20px 60px rgba(255, 215, 0, 0.4),
        0 10px 30px rgba(0, 0, 0, 0.5),
        inset 0 2px 4px rgba(255, 215, 0, 0.5);
}

.dark-theme .logo-container i {
    color: #FFD700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.dark-theme .logo-text {
    color: #FFD700;
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5), 0 0 20px rgba(255, 215, 0, 0.3);
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-10px) scale(1.05);
    }
}

/* Bounce In Animation */
@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.5) translateY(30px);
    }
    60% {
        opacity: 1;
        transform: scale(1.08) translateY(-5px);
    }
    80% {
        transform: scale(0.95) translateY(2px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Grid Layout Animation */
@keyframes gridFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Apps Container */
.apps-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: 500;
    overflow: visible;
    padding: 0;
}

.apps-container.show {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    pointer-events: auto !important;
    z-index: 600 !important;
    visibility: visible !important;
}

.apps-container.show .app-item {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
}

/* Force show apps when container has show class */
.apps-container.show {
    display: block !important;
}

.apps-container.show .apps-page {
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

.apps-container.show .apps-page .app-item {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Apps Page */
.apps-page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    padding: 0;
    overflow: visible;
}

.apps-page.active {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

/* App Item */
.app-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: grab;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: absolute;
    padding: 5px;
    z-index: 700;
    width: 70px;
    height: 90px;
    user-select: none;
    touch-action: none;
}

.app-item:active {
    cursor: grabbing;
}

.app-item.dragging {
    z-index: 1000;
    transform: scale(1.1);
    opacity: 0.8;
    position: fixed !important;
}

.app-item:hover {
    transform: scale(1.1) translateY(-5px);
}

.app-item:active {
    transform: scale(0.95);
}

/* App Icon */
.app-icon {
    width: 55px;
    height: 55px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    z-index: 800;
    box-shadow:
        0 20px 50px rgba(0, 0, 0, 0.5),
        0 10px 25px rgba(0, 0, 0, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.3),
        inset 0 3px 6px rgba(255, 255, 255, 0.4),
        inset 0 -3px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* App Shine Effect */
.app-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.6s ease;
}

.app-item:hover .app-icon {
    transform: scale(1.15) translateY(-3px);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.2);
}

.app-item:hover .app-shine {
    transform: translateX(100%) translateY(100%) rotate(45deg);
}

/* App Name */
.app-name {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.95);
    text-align: center;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
    line-height: 1.2;
    max-width: 70px;
    word-wrap: break-word;
    margin-top: 4px;
    letter-spacing: 0.2px;
    z-index: 15;
    position: relative;
}

.dark-theme .app-name {
    color: #FFD700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px rgba(255, 215, 0, 0.3);
}

/* إزالة خلفيات الأيقونات في الوضع الليلي */

/* Tooltip */
.app-item::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 10px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 1000;
}

.app-item:hover::before {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

/* Social Media App Icon Colors */
.whatsapp {
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
}

.instagram {
    background: linear-gradient(135deg, #E4405F 0%, #833AB4 50%, #F77737 100%);
}

.facebook {
    background: linear-gradient(135deg, #1877F2 0%, #42A5F5 100%);
}

.twitter {
    background: linear-gradient(135deg, #1DA1F2 0%, #0D8BD9 100%);
}

.youtube {
    background: linear-gradient(135deg, #FF0000 0%, #CC0000 100%);
}

.tiktok {
    background: linear-gradient(135deg, #000000 0%, #ff0050 50%, #00f2ea 100%);
}

.linkedin {
    background: linear-gradient(135deg, #0077B5 0%, #005885 100%);
}

.telegram {
    background: linear-gradient(135deg, #0088cc 0%, #006699 100%);
}

.snapchat {
    background: linear-gradient(135deg, #FFFC00 0%, #FFD700 100%);
    color: #333 !important;
}

.phone-app {
    background: linear-gradient(135deg, #34C759 0%, #28A745 100%);
}

.email-app {
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
}

.website-app {
    background: linear-gradient(135deg, #5856D6 0%, #4A4A9D 100%);
}

.camera-app {
    background: linear-gradient(135deg, #8E8E93 0%, #636366 100%);
}

.gallery-app {
    background: linear-gradient(135deg, #FF9500 0%, #FF6B00 100%);
}

.settings {
    background: linear-gradient(135deg, #8E8E93 0%, #636366 100%);
}

.weather {
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
}

.file-manager {
    background: linear-gradient(135deg, #34C759 0%, #28A745 100%);
}

.notes {
    background: linear-gradient(135deg, #FFCC02 0%, #FFB000 100%);
}

/* Page 2 App Colors */
.google-assistant {
    background: linear-gradient(135deg, #4285F4 0%, #34A853 25%, #FBBC05 50%, #EA4335 75%, #4285F4 100%);
}

.settings {
    background: linear-gradient(135deg, #8E8E93 0%, #636366 100%);
}

.google-play {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 25%, #FFD23F 50%, #00A651 75%, #1976D2 100%);
}

.weather {
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 100%);
}

.file-manager {
    background: linear-gradient(135deg, #FFB347 0%, #FF8C00 100%);
}

.notes {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.trash {
    background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
}

/* Page Indicators */
.page-indicators {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 250;
    padding: 6px 12px;
    border-radius: 15px;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.indicator.active {
    background: rgba(255, 255, 255, 0.95);
    transform: scale(1.3);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.4),
        0 0 0 2px rgba(255, 255, 255, 0.3);
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
}

.dark-theme .indicator {
    background: rgba(255, 215, 0, 0.5);
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.dark-theme .indicator.active {
    background: #FFD700;
    box-shadow:
        0 4px 12px rgba(255, 215, 0, 0.6),
        0 0 0 2px rgba(255, 215, 0, 0.5);
}

.dark-theme .indicator:hover {
    background: rgba(255, 215, 0, 0.8);
}

.dark-theme .camera-app {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.dark-theme .gallery-app {
    background: linear-gradient(135deg, #FFD700 0%, #FF8C00 100%);
}

.dark-theme .settings {
    background: linear-gradient(135deg, #FFD700 0%, #B8860B 100%);
}

.dark-theme .weather {
    background: linear-gradient(135deg, #FFD700 0%, #DAA520 100%);
}

.dark-theme .file-manager {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.dark-theme .notes {
    background: linear-gradient(135deg, #FFD700 0%, #FFCC02 100%);
}

.dark-theme .camera-shortcut,
.dark-theme .flashlight-shortcut {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    color: #FFD700;
}

.dark-theme .camera-shortcut:hover,
.dark-theme .flashlight-shortcut:hover {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 215, 0, 0.1) 100%);
    box-shadow:
        0 8px 25px rgba(255, 215, 0, 0.4),
        0 4px 15px rgba(255, 215, 0, 0.2),
        inset 0 1px 2px rgba(255, 215, 0, 0.3);
}

/* Flashlight pulse animation */
@keyframes flashlightPulse {
    0%, 100% {
        box-shadow:
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 40px rgba(255, 215, 0, 0.6),
            0 0 60px rgba(255, 215, 0, 0.4),
            inset 0 1px 2px rgba(255, 255, 255, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow:
            0 0 30px rgba(255, 215, 0, 1),
            0 0 60px rgba(255, 215, 0, 0.8),
            0 0 90px rgba(255, 215, 0, 0.6),
            inset 0 1px 2px rgba(255, 255, 255, 0.4);
        transform: scale(1.05);
    }
}

/* Dark theme for widgets */
.dark-theme .widget {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.dark-theme .battery-container {
    border-color: rgba(255, 215, 0, 0.8);
}

.dark-theme .battery-tip {
    background: rgba(255, 215, 0, 0.8);
}

.dark-theme .battery-percentage {
    color: #FFD700;
}

.dark-theme .weather-widget {
    color: #FFD700;
}

.dark-theme .weather-temp {
    color: #FFD700;
}

.dark-theme .weather-location {
    color: #FFD700;
}

/* Dark theme for status bar icons */
.dark-theme .signal-bar {
    background: #FFD700;
    box-shadow: 0 0 4px rgba(255, 215, 0, 0.5);
}

.dark-theme .wifi-icon {
    color: #FFD700;
    text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
}

.dark-theme .battery-container {
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.dark-theme .battery-icon {
    border-color: #FFD700;
}

.dark-theme .battery-tip {
    background: #FFD700;
}

.dark-theme .battery-percentage {
    color: #FFD700;
    text-shadow: 0 0 6px rgba(255, 215, 0, 0.4);
}

/* Dock */
.dock {
    position: absolute;
    bottom: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 280px;
    height: 75px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.02) 100%);
    backdrop-filter: blur(30px);
    border-radius: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 25px;
    gap: 20px;
    box-shadow:
        0 15px 45px rgba(0, 0, 0, 0.35),
        0 8px 25px rgba(0, 0, 0, 0.25),
        inset 0 1px 2px rgba(255, 255, 255, 0.25),
        inset 0 -1px 2px rgba(0, 0, 0, 0.08);
    z-index: 20;
    border: 1px solid rgba(255, 255, 255, 0.15);
    animation: dockFloat 4s ease-in-out infinite;
    transition: all 0.3s ease;
}

/* إخفاء الرصيف في شاشة القفل */
.phone.locked .dock {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(20px);
}

.dark-theme .dock {
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.4) 100%);
    border: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow:
        0 12px 40px rgba(255, 215, 0, 0.2),
        0 6px 20px rgba(0, 0, 0, 0.5),
        inset 0 1px 2px rgba(255, 215, 0, 0.15),
        inset 0 -1px 2px rgba(0, 0, 0, 0.2);
}

@keyframes dockFloat {
    0%, 100% {
        transform: translateX(-50%) translateY(0px);
    }
    50% {
        transform: translateX(-50%) translateY(-3px);
    }
}

.dock-icon {
    width: 58px;
    height: 58px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    color: white;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.35),
        0 5px 15px rgba(0, 0, 0, 0.25),
        inset 0 2px 4px rgba(255, 255, 255, 0.25),
        inset 0 -2px 4px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.18);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.04) 100%);
}

/* Dock Shine Effect */
.dock-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.6s ease;
}

.dock-icon:hover {
    transform: scale(1.25) translateY(-15px) rotateY(10deg);
    box-shadow:
        0 25px 60px rgba(0, 0, 0, 0.5),
        0 15px 35px rgba(0, 0, 0, 0.4),
        inset 0 4px 8px rgba(255, 255, 255, 0.4),
        inset 0 -4px 8px rgba(0, 0, 0, 0.2);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
}

.dock-icon:hover .dock-shine {
    transform: translateX(100%) translateY(100%) rotate(45deg);
}

.dock-icon:active {
    transform: scale(1.1) translateY(-5px);
}

.phone {
    background: linear-gradient(135deg, #34C759 0%, #28A745 100%);
}

.messages {
    background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
}

/* Home Button */
.home-button {
    width: 65px;
    height: 65px;
    background: linear-gradient(145deg, #1a1a1a 0%, #000000 50%, #0a0a0a 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.55),
        0 8px 20px rgba(0, 0, 0, 0.35),
        inset 0 3px 6px rgba(255, 255, 255, 0.18),
        inset 0 -3px 6px rgba(0, 0, 0, 0.45);
    border: 2px solid rgba(255, 255, 255, 0.12);
    animation: homeButtonPulse 3s ease-in-out infinite;
}

@keyframes homeButtonPulse {
    0%, 100% {
        box-shadow:
            0 20px 50px rgba(0, 0, 0, 0.6),
            0 10px 25px rgba(0, 0, 0, 0.4),
            inset 0 4px 8px rgba(255, 255, 255, 0.2),
            inset 0 -4px 8px rgba(0, 0, 0, 0.5);
    }
    50% {
        box-shadow:
            0 25px 60px rgba(0, 0, 0, 0.7),
            0 15px 35px rgba(0, 0, 0, 0.5),
            inset 0 5px 10px rgba(255, 255, 255, 0.25),
            inset 0 -5px 10px rgba(0, 0, 0, 0.6);
    }
}

/* Home Shine Effect */
.home-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    transition: transform 0.6s ease;
}

.home-indicator {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 50%, #**********%);
    border: 2px solid rgba(255, 255, 255, 0.22);
    box-shadow:
        inset 0 3px 6px rgba(255, 255, 255, 0.18),
        inset 0 -3px 6px rgba(0, 0, 0, 0.35),
        0 0 12px rgba(255, 255, 255, 0.12);
    transition: all 0.4s ease;
    z-index: 2;
    position: relative;
}

.home-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%);
    box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.25);
}

.home-button:hover {
    transform: scale(1.2) translateY(-5px);
    box-shadow:
        0 30px 70px rgba(0, 0, 0, 0.7),
        0 15px 40px rgba(0, 0, 0, 0.5),
        inset 0 5px 10px rgba(255, 255, 255, 0.25),
        inset 0 -5px 10px rgba(0, 0, 0, 0.6);
    border: 3px solid rgba(255, 255, 255, 0.25);
}

.home-button:hover .home-shine {
    transform: translateX(100%) translateY(100%) rotate(45deg);
}

.home-button:hover .home-indicator {
    background: linear-gradient(135deg, #444 0%, #222 50%, #111 100%);
    box-shadow:
        inset 0 3px 6px rgba(255, 255, 255, 0.25),
        inset 0 -3px 6px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 255, 255, 0.15);
    transform: scale(1.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.home-button:hover .home-indicator::before {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
    box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        0 0 10px rgba(255, 255, 255, 0.2);
}

.home-button:active {
    transform: scale(1.05);
}

.home-button:active .home-indicator {
    transform: scale(0.95);
}

.home-indicator-bottom {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

/* QR Code Section */
.qr-section {
    flex: 1;
    max-width: 400px;
    text-align: center;
    color: white;
}

.qr-code {
    width: 200px;
    height: 200px;
    background: linear-gradient(145deg, #ffffff 0%, #f8f8f8 50%, #f0f0f0 100%);
    border-radius: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 100px;
    color: #333;
    margin: 0 auto 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 10px 30px rgba(0, 0, 0, 0.2),
        inset 0 4px 8px rgba(255, 255, 255, 0.8),
        inset 0 -4px 8px rgba(0, 0, 0, 0.1);
    animation: qrPulse 3s ease-in-out infinite;
    transition: all 0.3s ease;
}

.qr-code:hover {
    transform: scale(1.05) rotateY(-10deg);
}

@keyframes qrPulse {
    0%, 100% {
        transform: scale(1) rotateX(0deg);
    }
    50% {
        transform: scale(1.02) rotateX(5deg);
    }
}

.qr-text {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Contact Info */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 16px;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.contact-item i {
    font-size: 18px;
    color: #ffffff;
}

/* Dark Mode */
.dark-mode {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.dark-mode .theme-btn {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #333;
}

.dark-mode .company-logo {
    background: linear-gradient(145deg, #2c2c2c 0%, #1a1a1a 50%, #0a0a0a 100%);
    color: #ffd700;
}

.dark-mode .qr-code {
    background: linear-gradient(145deg, #2c2c2c 0%, #1a1a1a 50%, #0a0a0a 100%);
    color: #ffd700;
}

.dark-mode .wave {
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-container {
        gap: 40px;
    }

    .company-logo {
        width: 150px;
        height: 150px;
        font-size: 80px;
    }

    .qr-code {
        width: 140px;
        height: 140px;
        font-size: 75px;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
        gap: 30px;
        padding: 20px 10px;
    }

    .company-section,
    .qr-section {
        max-width: 100%;
    }

    .company-logo {
        width: 120px;
        height: 120px;
        font-size: 60px;
    }

    .company-name {
        font-size: 28px;
    }

    .company-slogan {
        font-size: 16px;
    }

    .company-stats {
        gap: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .phone {
        width: 280px;
        height: 510px;
    }

    .qr-code {
        width: 120px;
        height: 120px;
        font-size: 60px;
    }

    .qr-text {
        font-size: 16px;
    }

    .contact-item {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .theme-toggle {
        top: 10px;
        right: 10px;
    }

    .theme-btn {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .main-container {
        padding: 15px 5px;
        gap: 20px;
    }

    .company-logo {
        width: 100px;
        height: 100px;
        font-size: 50px;
    }

    .company-name {
        font-size: 24px;
    }

    .company-slogan {
        font-size: 14px;
    }

    .company-stats {
        flex-direction: column;
        gap: 15px;
    }

    .stat-number {
        font-size: 20px;
    }

    .phone {
        width: 250px;
        height: 450px;
    }

    .qr-code {
        width: 100px;
        height: 100px;
        font-size: 50px;
    }

    .qr-text {
        font-size: 14px;
    }

    .contact-info {
        gap: 10px;
    }

    .contact-item {
        font-size: 12px;
        padding: 8px 15px;
    }
}

/* Unlock Indicator - Only in Lock Screen */
.unlock-indicator {
    position: absolute;
    bottom: 110px;
    left: 20px;
    transform: translateX(0);
    color: rgba(255, 255, 255, 0.9);
    font-size: 17px;
    font-weight: 600;
    text-align: center;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 300;
    display: flex;
    align-items: center;
    gap: 12px;
    background: rgba(0, 0, 0, 0.4);
    padding: 16px 28px;
    border-radius: 30px;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.25);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    letter-spacing: 0.5px;
    cursor: grab;
    user-select: none;
}

.unlock-indicator.show {
    opacity: 1;
    animation: unlockPulse 3s infinite ease-in-out;
}

/* Animated Arrow */
.animated-arrow {
    animation: arrowMove 1.5s infinite ease-in-out;
    margin-left: 8px;
}

@keyframes arrowMove {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(8px);
    }
}

/* Mobile optimizations for unlock indicator */
@media (max-width: 768px) {
    .unlock-indicator {
        bottom: 100px;
        left: 15px;
        width: auto;
        min-width: 200px;
        padding: 12px 20px;
        font-size: 15px;
        gap: 10px;
        border-radius: 25px;
    }

    .unlock-text {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .unlock-indicator {
        bottom: 90px;
        left: 10px;
        right: 10px;
        width: auto;
        min-width: auto;
        padding: 10px 16px;
        font-size: 14px;
        text-align: center;
    }

    .unlock-text {
        font-size: 13px;
    }
}

.unlock-indicator.dragging {
    cursor: grabbing;
    transform: translateX(-50%) scale(1.1);
    background: rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.4);
}

.unlock-indicator i {
    font-size: 20px;
    animation: slideUnlock 3s infinite ease-in-out;
    color: rgba(255, 255, 255, 0.95);
    pointer-events: none;
}

.unlock-indicator .unlock-text {
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    pointer-events: none;
}

.dark-theme .unlock-indicator {
    background: rgba(0, 0, 0, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.dark-theme .unlock-indicator i {
    color: #FFD700;
}

.dark-theme .unlock-indicator .unlock-text {
    color: #FFF8DC;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}



@keyframes slideUnlock {
    0%, 100% {
        transform: translateX(0) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: translateX(15px) scale(1.1);
        opacity: 1;
    }
    50% {
        transform: translateX(30px) scale(1.2);
        opacity: 1;
    }
    75% {
        transform: translateX(15px) scale(1.1);
        opacity: 1;
    }
}

@keyframes unlockPulse {
    0%, 100% {
        box-shadow:
            0 10px 30px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            0 0 0 0 rgba(255, 255, 255, 0);
        background: rgba(0, 0, 0, 0.4);
        border-color: rgba(255, 255, 255, 0.25);
    }
    50% {
        box-shadow:
            0 15px 40px rgba(0, 0, 0, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            0 0 0 8px rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.5);
        border-color: rgba(255, 255, 255, 0.4);
    }
}

/* Phone Side Buttons */
.phone-buttons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 5;
}

/* Phone Details - Realistic Elements */
.phone-details {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 15;
}

.front-camera {
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, #1a1a1a 0%, #000000 70%, #333333 100%);
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: -30px;
    box-shadow:
        inset 0 1px 2px rgba(255, 255, 255, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.5);
}

.speaker-grill {
    width: 40px;
    height: 4px;
    background: linear-gradient(90deg, #1a1a1a 0%, #333333 50%, #1a1a1a 100%);
    border-radius: 2px;
    position: absolute;
    top: 2px;
    left: -20px;
    box-shadow:
        inset 0 1px 1px rgba(0, 0, 0, 0.5),
        0 1px 2px rgba(255, 255, 255, 0.05);
}

/* Power Button */
.power-button {
    position: absolute;
    right: -8px;
    top: 120px;
    width: 6px;
    height: 60px;
    background: linear-gradient(90deg, #666 0%, #999 50%, #666 100%);
    border-radius: 3px 6px 6px 3px;
    box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.3),
        2px 0 8px rgba(0, 0, 0, 0.2);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.2s ease;
}

.power-button:hover {
    background: linear-gradient(90deg, #777 0%, #aaa 50%, #777 100%);
    transform: translateX(1px);
}

.power-button:active {
    background: linear-gradient(90deg, #555 0%, #888 50%, #555 100%);
    transform: translateX(2px);
}

/* Volume Buttons */
.volume-up {
    position: absolute;
    left: -8px;
    top: 100px;
    width: 6px;
    height: 45px;
    background: linear-gradient(270deg, #666 0%, #999 50%, #666 100%);
    border-radius: 6px 3px 3px 6px;
    box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.3),
        -2px 0 8px rgba(0, 0, 0, 0.2);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.2s ease;
}

.volume-down {
    position: absolute;
    left: -8px;
    top: 155px;
    width: 6px;
    height: 45px;
    background: linear-gradient(270deg, #666 0%, #999 50%, #666 100%);
    border-radius: 6px 3px 3px 6px;
    box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.3),
        inset 0 -2px 4px rgba(0, 0, 0, 0.3),
        -2px 0 8px rgba(0, 0, 0, 0.2);
    pointer-events: auto;
    cursor: pointer;
    transition: all 0.2s ease;
}

.volume-up:hover,
.volume-down:hover {
    background: linear-gradient(270deg, #777 0%, #aaa 50%, #777 100%);
    transform: translateX(-1px);
}

.volume-up:active,
.volume-down:active {
    background: linear-gradient(270deg, #555 0%, #888 50%, #555 100%);
    transform: translateX(-2px);
}

/* Dark Theme Button Colors - Black with Gold accents */
.dark-theme .power-button {
    background: linear-gradient(90deg, #1a1a1a 0%, #FFD700 50%, #1a1a1a 100%);
    border: 1px solid #FFD700;
}

.dark-theme .volume-up,
.dark-theme .volume-down {
    background: linear-gradient(270deg, #1a1a1a 0%, #FFD700 50%, #1a1a1a 100%);
    border: 1px solid #FFD700;
}

.dark-theme .power-button:hover {
    background: linear-gradient(90deg, #333 0%, #FFA500 50%, #333 100%);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.dark-theme .volume-up:hover,
.dark-theme .volume-down:hover {
    background: linear-gradient(270deg, #333 0%, #FFA500 50%, #333 100%);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

/* Light Theme Button Colors (Titanium Phone) */
.light-theme .power-button {
    background: linear-gradient(90deg, #878684 0%, #a8a6a3 50%, #878684 100%);
}

.light-theme .volume-up,
.light-theme .volume-down {
    background: linear-gradient(270deg, #878684 0%, #a8a6a3 50%, #878684 100%);
}

.light-theme .power-button:hover {
    background: linear-gradient(90deg, #9a9895 0%, #bbb9b6 50%, #9a9895 100%);
}

.light-theme .volume-up:hover,
.light-theme .volume-down:hover {
    background: linear-gradient(270deg, #9a9895 0%, #bbb9b6 50%, #9a9895 100%);
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
    .phone {
        width: 300px;
        height: 550px;
        transform: perspective(1000px) rotateX(5deg) rotateY(-5deg);
    }

    .power-button {
        right: -6px;
        width: 5px;
        height: 50px;
        top: 110px;
    }

    .volume-up {
        left: -6px;
        width: 5px;
        height: 40px;
        top: 90px;
    }

    .volume-down {
        left: -6px;
        width: 5px;
        height: 40px;
        top: 140px;
    }

    .battery-container {
        gap: 3px;
    }

    .battery-icon {
        width: 22px;
        height: 11px;
    }

    .battery-percentage {
        font-size: 11px;
        padding: 2px 5px;
        min-width: 26px;
    }

    .dock {
        width: 260px;
        height: 70px;
        padding: 10px 22px;
        gap: 18px;
    }

    .dock-icon {
        width: 54px;
        height: 54px;
        font-size: 24px;
    }

    .home-button {
        width: 60px;
        height: 60px;
    }

    .home-indicator {
        width: 44px;
        height: 44px;
    }
}

@media (max-width: 480px) {
    .phone {
        width: 280px;
        height: 520px;
        transform: perspective(800px) rotateX(3deg) rotateY(-3deg);
    }

    .power-button {
        right: -5px;
        width: 4px;
        height: 45px;
        top: 100px;
    }

    .volume-up {
        left: -5px;
        width: 4px;
        height: 35px;
        top: 85px;
    }

    .volume-down {
        left: -5px;
        width: 4px;
        height: 35px;
        top: 130px;
    }

    .battery-container {
        gap: 2px;
    }

    .battery-icon {
        width: 18px;
        height: 9px;
    }

    .battery-percentage {
        font-size: 9px;
        padding: 1px 3px;
        min-width: 20px;
    }

    .dock {
        width: 240px;
        height: 65px;
        padding: 8px 18px;
        gap: 15px;
    }

    .dock-icon {
        width: 50px;
        height: 50px;
        font-size: 22px;
    }

    .home-button {
        width: 55px;
        height: 55px;
    }

    .home-indicator {
        width: 40px;
        height: 40px;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .app-item:hover {
        transform: none;
    }

    .app-item:active {
        transform: scale(0.95);
    }

    .power-button:hover,
    .volume-up:hover,
    .volume-down:hover {
        transform: none;
    }

    .contact-item:hover {
        transform: none;
    }

    .contact-item:active {
        transform: scale(0.98);
    }

    .social-icon:hover {
        transform: none;
    }

    .social-icon:active {
        transform: scale(0.95);
    }

    .dock-icon:hover {
        transform: none;
    }

    .dock-icon:active {
        transform: scale(0.95);
    }
}

/* Ultra-wide screens */
@media (min-width: 1400px) {
    .phone {
        width: 350px;
        height: 650px;
    }

    .battery-icon {
        width: 24px;
        height: 12px;
    }

    .battery-percentage {
        font-size: 12px;
        padding: 2px 6px;
        min-width: 28px;
    }

    .dock {
        width: 320px;
        height: 85px;
        padding: 15px 30px;
        gap: 25px;
    }

    .dock-icon {
        width: 65px;
        height: 65px;
        font-size: 30px;
    }

    .home-button {
        width: 75px;
        height: 75px;
    }

    .home-indicator {
        width: 55px;
        height: 55px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .battery-icon {
        border-width: 1.5px;
    }

    .battery-tip {
        width: 2.5px;
    }
}
