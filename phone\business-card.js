// Global Variables
let currentPage = 1;
const totalPages = 2;
let batteryLevel = Math.floor(Math.random() * 30) + 70; // Random battery between 70-100%
let isDarkMode = true; // Start with dark theme
let isLocked = true; // العودة لشاشة القفل

// Initialize AOS and other libraries
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS (Animate On Scroll)
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });

    // Initialize functions
    initializeTheme(); // Set dark theme as default
    updateTime();
    updateDate();
    updateLockTime();
    updateBattery();
    initParticles();
    animateStats();
    setupAppClickEvents();
    setupCameraFunction();

    // Set initial locked state
    const phone = document.querySelector('.phone');
    if (phone && isLocked) {
        phone.classList.add('locked');
    }

    // Update time every second for real-time
    setInterval(() => {
        updateTime();
        updateLockTime();
    }, 1000);

    // Update date every minute
    setInterval(updateDate, 60000);

    // Update battery every 30 seconds (decrease by 1%)
    setInterval(() => {
        if (batteryLevel > 1) {
            batteryLevel--;
            updateBattery();
        }
    }, 30000);

    // Swipe functionality for mobile
    let startX = 0;
    let endX = 0;

    const appsContainer = document.getElementById('appsContainer');
    if (appsContainer) {
        appsContainer.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        });

        appsContainer.addEventListener('touchend', (e) => {
            endX = e.changedTouches[0].clientX;
            const swipeThreshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0 && currentPage < totalPages) {
                    switchPage(currentPage + 1);
                } else if (diff < 0 && currentPage > 1) {
                    switchPage(currentPage - 1);
                }
            }
        });
    }

    // Click on indicators
    document.querySelectorAll('.indicator').forEach(indicator => {
        indicator.addEventListener('click', () => {
            const pageNumber = parseInt(indicator.getAttribute('data-page'));
            switchPage(pageNumber);
        });
    });

    // Theme toggle
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Home button click event
    const homeButton = document.getElementById('homeButton');
    if (homeButton) {
        homeButton.addEventListener('click', handleHomeButtonClick);
        console.log('Home button event listener added');
    }

    // Wallpaper click - only hide apps, no unlock
    const wallpaper = document.getElementById('wallpaper');
    if (wallpaper) {
        wallpaper.addEventListener('click', handleWallpaperClick);
    }

    // Lock screen - disable direct unlock
    const lockScreen = document.getElementById('lockScreen');
    if (lockScreen) {
        // Remove all direct unlock functionality
        // Only allow unlock via swipe indicator
        console.log('Lock screen initialized - unlock only via swipe indicator');
    }

    // Parallax effect on mouse move
    document.addEventListener('mousemove', (e) => {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;

        // Move background waves
        const waves = document.querySelectorAll('.wave');
        waves.forEach((wave, index) => {
            const speed = (index + 1) * 0.5;
            wave.style.transform = `translate(${mouseX * speed}px, ${mouseY * speed}px) rotate(${index * 120}deg)`;
        });

        // Move phone slightly
        const phone = document.getElementById('phone');
        if (phone) {
            const moveX = (mouseX - 0.5) * 10;
            const moveY = (mouseY - 0.5) * 10;
            phone.style.transform = `perspective(1000px) rotateX(${5 + moveY}deg) rotateY(${-5 + moveX}deg)`;
        }
    });
});

// Update Time Function
function updateTime() {
    const now = new Date();
    let hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // 0 should be 12

    const timeString = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
    document.getElementById('currentTime').textContent = timeString;
}

// Update Date Function
function updateDate() {
    const now = new Date();
    const options = {
        weekday: 'long',
        month: 'long',
        day: 'numeric'
    };
    const dateString = now.toLocaleDateString('en-US', options);
    const currentDateEl = document.getElementById('currentDate');
    if (currentDateEl) {
        currentDateEl.textContent = dateString;
    }
}

// Update Lock Screen Time
function updateLockTime() {
    const now = new Date();
    let hours = now.getHours();
    const minutes = now.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';

    hours = hours % 12;
    hours = hours ? hours : 12; // 0 should be 12

    const timeString = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
    const lockHourEl = document.getElementById('lockHour');
    const lockDateEl = document.getElementById('lockDate');

    if (lockHourEl) {
        lockHourEl.textContent = timeString;
    }

    if (lockDateEl) {
        const options = {
            weekday: 'long',
            month: 'long',
            day: 'numeric'
        };
        const dateString = now.toLocaleDateString('en-US', options);
        lockDateEl.textContent = dateString;
    }
}

// Handle Home Button Click
function handleHomeButtonClick() {
    if (isLocked) {
        unlockPhone();
    } else {
        // إذا كان الهاتف مفتوح، إظهار/إخفاء الأيقونات
        const appsContainer = document.getElementById('appsContainer');
        const centerLogo = document.getElementById('centerLogo');

        if (appsContainer && appsContainer.classList.contains('show')) {
            // إخفاء الأيقونات وإظهار الشعار
            toggleApps(true);
            setTimeout(() => {
                if (centerLogo) {
                    centerLogo.classList.add('show');
                }
            }, 300);
        } else {
            // إخفاء الشعار وإظهار الأيقونات
            if (centerLogo) {
                centerLogo.classList.remove('show');
            }
            setTimeout(() => {
                toggleApps(true);
            }, 200);
        }
    }
}

// Handle Wallpaper Click
function handleWallpaperClick() {
    if (!isLocked) {
        const appsContainer = document.getElementById('appsContainer');
        if (appsContainer && appsContainer.classList.contains('show')) {
            toggleApps(true); // Allow hiding apps
        }
    }
    // If locked, do nothing - only unlock via swipe indicator
}

// Unlock Phone Function
function unlockPhone() {
    if (!isLocked) return;

    isLocked = false;
    const lockScreen = document.getElementById('lockScreen');
    const centerLogo = document.getElementById('centerLogo');
    const phone = document.querySelector('.phone');

    // إزالة كلاس locked لإظهار الرصيف
    if (phone) {
        phone.classList.remove('locked');
    }

    if (lockScreen) {
        lockScreen.style.transform = 'translateY(-100%)';
        lockScreen.style.opacity = '0';
        setTimeout(() => {
            lockScreen.style.display = 'none';
            // إظهار الشعار في الوسط فقط
            if (centerLogo) {
                centerLogo.classList.add('show');
            }

            // إظهار مؤشر فتح القفل بعد فترة قصيرة
            setTimeout(() => {
                const unlockIndicator = document.getElementById('unlockIndicator');
                if (unlockIndicator) {
                    unlockIndicator.classList.add('show');
                }
            }, 1000);
        }, 500);
    }

    playClickSound();
    console.log('Phone unlocked - showing logo only');
}

// Lock Phone Function
function lockPhone() {
    isLocked = true;
    const phone = document.querySelector('.phone');
    const lockScreen = document.getElementById('lockScreen');
    const centerLogo = document.getElementById('centerLogo');
    const appsContainer = document.getElementById('appsContainer');

    // إضافة كلاس locked لإخفاء الرصيف
    if (phone) {
        phone.classList.add('locked');
    }

    // إخفاء العناصر
    if (centerLogo) {
        centerLogo.classList.remove('show');
    }

    if (appsContainer) {
        appsContainer.classList.remove('show');
    }

    // إظهار شاشة القفل
    if (lockScreen) {
        lockScreen.style.display = 'flex';
        lockScreen.style.transform = 'translateY(0)';
        lockScreen.style.opacity = '1';
    }

    console.log('Phone locked');
}

// Setup App Click Events
function setupAppClickEvents() {
    const appItems = document.querySelectorAll('.app-item');

    appItems.forEach(app => {
        app.addEventListener('click', function(e) {
            // Only trigger click if not dragging
            if (!this.classList.contains('dragging')) {
                const url = this.getAttribute('data-url');
                const tooltip = this.getAttribute('data-tooltip');

                playClickSound();

                if (url) {
                    // Add opening animation
                    this.style.transform = 'scale(0.8)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                        window.open(url, '_blank');
                    }, 150);
                }

                console.log(`Clicked on ${tooltip}`);
            }
        });
    });
}

// Update Battery Function
function updateBattery() {
    const batteryLevelEl = document.getElementById('batteryLevel');
    const batteryPercentageEl = document.getElementById('batteryPercentage');
    const lockBatteryEl = document.getElementById('lockBattery');

    if (batteryLevelEl && batteryPercentageEl) {
        batteryLevelEl.style.width = `${batteryLevel}%`;
        batteryPercentageEl.textContent = `${batteryLevel}%`;

        // Change color based on battery level
        if (batteryLevel > 50) {
            batteryLevelEl.style.backgroundColor = '#34c759'; // Green
        } else if (batteryLevel > 20) {
            batteryLevelEl.style.backgroundColor = '#ff9500'; // Orange
        } else {
            batteryLevelEl.style.backgroundColor = '#ff3b30'; // Red
        }
    }

    // Update lock screen battery
    if (lockBatteryEl) {
        lockBatteryEl.textContent = `${batteryLevel}%`;
    }
}

// Toggle Apps Function - Only allow unlock via swipe
function toggleApps(allowUnlock = false) {
    const appsContainer = document.getElementById('appsContainer');
    const wallpaper = document.getElementById('wallpaper');
    const homeButton = document.getElementById('homeButton');
    const centerLogo = document.getElementById('centerLogo');

    if (appsContainer) {
        const isVisible = appsContainer.classList.contains('show');

        if (isVisible) {
            // Hide apps with animation (lock the phone)
            appsContainer.style.transform = 'translateY(30px) scale(0.9)';
            appsContainer.style.opacity = '0';
            setTimeout(() => {
                appsContainer.classList.remove('show');
                // إظهار الشعار مرة أخرى
                if (centerLogo) {
                    centerLogo.classList.add('show');
                }

                // Show unlock indicator again after delay
                setTimeout(() => {
                    const unlockIndicator = document.getElementById('unlockIndicator');
                    if (unlockIndicator) {
                        unlockIndicator.classList.add('show');
                    }
                }, 2000);
            }, 300);
            if (wallpaper) {
                wallpaper.style.opacity = '1';
                wallpaper.style.filter = 'blur(0px)';
            }

            playClickSound();
            console.log('Apps locked');
        } else if (allowUnlock) {
            // Show apps with animation - only if unlock is allowed
            // إخفاء الشعار
            if (centerLogo) {
                centerLogo.classList.remove('show');
            }

            // Hide unlock indicator
            const unlockIndicator = document.getElementById('unlockIndicator');
            if (unlockIndicator) {
                unlockIndicator.classList.remove('show');
            }

            appsContainer.classList.add('show');
            setTimeout(() => {
                appsContainer.style.transform = 'translateY(0) scale(1)';
                appsContainer.style.opacity = '1';

                // Animate each app icon with staggered effect
                const appItems = document.querySelectorAll('.app-item');
                appItems.forEach((app, index) => {
                    app.style.opacity = '0';
                    app.style.transform = 'scale(0.5) translateY(20px)';
                    setTimeout(() => {
                        app.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                        app.style.opacity = '1';
                        app.style.transform = 'scale(1) translateY(0)';
                        app.style.animation = 'bounceIn 0.5s ease forwards';
                    }, index * 50);
                });
            }, 50);

            if (wallpaper) {
                wallpaper.style.opacity = '0.2';
                wallpaper.style.filter = 'blur(5px)';
            }

            playClickSound();
            console.log('Apps unlocked via swipe');
        } else {
            // Prevent unlock without swipe
            console.log('Unlock prevented - use swipe gesture');
            return;
        }

        // Button press effect
        if (homeButton) {
            homeButton.style.transform = 'scale(0.9)';
            setTimeout(() => {
                homeButton.style.transform = 'scale(1)';
            }, 150);
        }
    }
}

// Switch Page Function
function switchPage(pageNumber) {
    document.querySelectorAll('.apps-page').forEach(page => {
        page.classList.remove('active');
    });

    document.getElementById(`page${pageNumber}`).classList.add('active');

    document.querySelectorAll('.indicator').forEach(indicator => {
        indicator.classList.remove('active');
    });
    document.querySelector(`[data-page="${pageNumber}"]`).classList.add('active');

    currentPage = pageNumber;
    playClickSound();
}

// Toggle Theme Function
function toggleTheme() {
    isDarkMode = !isDarkMode;
    document.body.classList.toggle('dark-theme', isDarkMode);
    document.body.classList.toggle('light-theme', !isDarkMode);

    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
        themeIcon.className = isDarkMode ? 'fas fa-sun' : 'fas fa-moon';
    }

    playClickSound();
    console.log('Theme toggled to:', isDarkMode ? 'dark' : 'light');
}

// Initialize theme on page load
function initializeTheme() {
    document.body.classList.add('dark-theme');
    const themeIcon = document.querySelector('#themeToggle i');
    if (themeIcon) {
        themeIcon.className = 'fas fa-sun';
    }
}

// Play Click Sound Function
function playClickSound() {
    // Create audio context for click sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);

    // Haptic feedback for mobile devices
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }
}

// Initialize Particles
function initParticles() {
    const particlesContainer = document.getElementById('particles');
    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = Math.random() * 4 + 1 + 'px';
        particle.style.height = particle.style.width;
        particle.style.background = 'rgba(255, 255, 255, 0.1)';
        particle.style.borderRadius = '50%';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `float ${Math.random() * 10 + 5}s linear infinite`;
        particle.style.animationDelay = Math.random() * 5 + 's';
        
        particlesContainer.appendChild(particle);
    }

    // Add CSS for particle animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// Animate Statistics Numbers
function animateStats() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(stat => {
        const target = parseInt(stat.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            stat.textContent = Math.floor(current);
        }, 20);
    });
}

// Make apps draggable (for desktop)
function makeDraggable() {
    const appItems = document.querySelectorAll('.app-item');
    
    appItems.forEach(app => {
        let isDragging = false;
        let startX, startY, initialX, initialY;
        
        app.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            initialX = app.offsetLeft;
            initialY = app.offsetTop;
            app.style.position = 'absolute';
            app.style.zIndex = '1000';
        });
        
        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                app.style.left = initialX + deltaX + 'px';
                app.style.top = initialY + deltaY + 'px';
            }
        });
        
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                app.style.zIndex = 'auto';
            }
        });
    });
}

// Setup Camera Function
function setupCameraFunction() {
    const cameraShortcut = document.querySelector('.camera-shortcut');

    if (cameraShortcut) {
        cameraShortcut.addEventListener('click', function(e) {
            e.stopPropagation(); // منع فتح القفل
            openCamera();
        });
    }
}

// Open Camera Function
function openCamera() {
    playClickSound();

    // تأثير بصري للكاميرا
    const cameraShortcut = document.querySelector('.camera-shortcut');
    if (cameraShortcut) {
        cameraShortcut.style.transform = 'scale(0.8)';
        setTimeout(() => {
            cameraShortcut.style.transform = 'scale(1)';
        }, 150);
    }

    // محاولة فتح الكاميرا
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(function(stream) {
                // إنشاء نافذة جديدة للكاميرا
                const newWindow = window.open('', '_blank', 'width=600,height=450');
                if (newWindow) {
                    newWindow.document.write(`
                        <!DOCTYPE html>
                        <html>
                            <head>
                                <title>الكاميرا 📸</title>
                                <style>
                                    body { margin:0; background:#000; display:flex; justify-content:center; align-items:center; font-family: Arial; }
                                    video { width:100%; height:100%; object-fit:cover; border-radius: 10px; }
                                    .controls { position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%); }
                                    button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; background: #007AFF; color: white; cursor: pointer; }
                                </style>
                            </head>
                            <body>
                                <video id="video" autoplay></video>
                                <div class="controls">
                                    <button onclick="takePhoto()">التقاط صورة 📷</button>
                                    <button onclick="window.close()">إغلاق</button>
                                </div>
                                <script>
                                    const video = document.getElementById('video');
                                    video.srcObject = arguments[0];

                                    function takePhoto() {
                                        const canvas = document.createElement('canvas');
                                        canvas.width = video.videoWidth;
                                        canvas.height = video.videoHeight;
                                        const ctx = canvas.getContext('2d');
                                        ctx.drawImage(video, 0, 0);

                                        // تحويل إلى رابط تحميل
                                        const link = document.createElement('a');
                                        link.download = 'photo_' + Date.now() + '.png';
                                        link.href = canvas.toDataURL();
                                        link.click();

                                        alert('تم التقاط الصورة! 📸');
                                    }
                                </script>
                            </body>
                        </html>
                    `);

                    // تمرير stream للنافذة الجديدة
                    newWindow.addEventListener('load', () => {
                        const video = newWindow.document.getElementById('video');
                        if (video) {
                            video.srcObject = stream;
                        }
                    });
                }
                console.log('Camera opened successfully');
            })
            .catch(function(error) {
                console.log('Camera access denied or not available:', error);
                // فتح تطبيق الكاميرا الافتراضي كبديل
                showCameraAlert();
            });
    } else {
        // للمتصفحات التي لا تدعم الكاميرا
        showCameraAlert();
    }
}

// Show Camera Alert
function showCameraAlert() {
    const alertDiv = document.createElement('div');
    alertDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.9), rgba(30, 30, 30, 0.9));
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            z-index: 10000;
            backdrop-filter: blur(20px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        ">
            <div style="font-size: 48px; margin-bottom: 15px;">📸</div>
            <div style="font-size: 18px; margin-bottom: 20px;">تم فتح تطبيق الكاميرا!</div>
            <button onclick="this.parentElement.parentElement.remove()" style="
                background: #007AFF;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 10px;
                cursor: pointer;
                font-size: 16px;
            ">موافق</button>
        </div>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 3000);

    console.log('Camera opened (simulated)');
}

// Initialize draggable functionality after DOM is loaded
setTimeout(makeDraggable, 2000);

// Initialize unlock indicator
function initUnlockIndicator() {
    // Show unlock indicator immediately
    setTimeout(() => {
        const unlockIndicator = document.getElementById('unlockIndicator');
        if (unlockIndicator) {
            unlockIndicator.classList.add('show');
        }
    }, 500);

    const unlockIndicator = document.getElementById('unlockIndicator');
    if (!unlockIndicator) return;

    let isDragging = false;
    let startX = 0;
    let currentX = 0;
    let dragDistance = 0;
    const unlockThreshold = 150; // Distance needed to unlock

    // Mouse events
    unlockIndicator.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);

    // Touch events
    unlockIndicator.addEventListener('touchstart', startDrag);
    document.addEventListener('touchmove', drag);
    document.addEventListener('touchend', endDrag);

    function startDrag(e) {
        isDragging = true;
        startX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
        unlockIndicator.classList.add('dragging');
        e.preventDefault();
    }

    function drag(e) {
        if (!isDragging) return;

        currentX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
        dragDistance = Math.max(0, currentX - startX); // Only allow right drag

        // Update visual feedback
        const progress = Math.min(dragDistance / unlockThreshold, 1);
        unlockIndicator.style.transform = `translateX(${dragDistance}px) scale(${1 + progress * 0.2})`;
        unlockIndicator.style.opacity = 1 - progress * 0.3;

        // Change background based on progress
        const bgOpacity = 0.4 + progress * 0.3;
        unlockIndicator.style.background = `rgba(0, 0, 0, ${bgOpacity})`;

        e.preventDefault();
    }

    function endDrag(e) {
        if (!isDragging) return;

        isDragging = false;
        unlockIndicator.classList.remove('dragging');

        if (dragDistance >= unlockThreshold) {
            // Unlock successful
            unlockIndicator.style.transform = 'translateX(200px) scale(0)';
            unlockIndicator.style.opacity = '0';

            setTimeout(() => {
                unlockIndicator.classList.remove('show');
                unlockIndicator.style.transform = 'translateX(0)';
                unlockIndicator.style.opacity = '';
                unlockIndicator.style.background = '';

                // فتح القفل
                unlockPhone();
            }, 300);
        } else {
            // Snap back
            unlockIndicator.style.transform = 'translateX(0)';
            unlockIndicator.style.opacity = '';
            unlockIndicator.style.background = '';
        }

        dragDistance = 0;
        e.preventDefault();
    }

    // Prevent direct unlock via logo click
    const centerLogo = document.getElementById('centerLogo');
    if (centerLogo) {
        centerLogo.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            // Only hide indicator, don't unlock
            console.log('Direct unlock prevented - use swipe gesture');
        });
    }
}

// Initialize unlock indicator when DOM is loaded
document.addEventListener('DOMContentLoaded', initUnlockIndicator);

// Phone Button Functions
function initPhoneButtons() {
    const powerButton = document.querySelector('.power-button');
    const volumeUp = document.querySelector('.volume-up');
    const volumeDown = document.querySelector('.volume-down');

    if (powerButton) {
        powerButton.addEventListener('click', () => {
            playClickSound();

            if (isLocked) {
                // إذا كان مقفل، لا نفعل شيء (أو يمكن إضافة تأثير بصري)
                const phone = document.querySelector('.phone');
                phone.style.filter = 'brightness(0.1)';
                setTimeout(() => {
                    phone.style.filter = '';
                }, 200);
                console.log('Power button pressed - phone is locked');
            } else {
                // إذا كان مفتوح، قفل الهاتف
                lockPhone();
                console.log('Power button pressed - phone locked');
            }
        });
    }

    if (volumeUp) {
        volumeUp.addEventListener('click', () => {
            playClickSound();
            showVolumeIndicator('+');
            console.log('Volume up pressed');
        });
    }

    if (volumeDown) {
        volumeDown.addEventListener('click', () => {
            playClickSound();
            showVolumeIndicator('-');
            console.log('Volume down pressed');
        });
    }
}

// Show Volume Indicator
function showVolumeIndicator(type) {
    const indicator = document.createElement('div');
    indicator.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 15px;
        font-size: 24px;
        z-index: 10000;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.2);
    `;
    indicator.innerHTML = `<i class="fas fa-volume-${type === '+' ? 'up' : 'down'}"></i> ${type === '+' ? 'رفع' : 'خفض'} الصوت`;

    document.body.appendChild(indicator);

    setTimeout(() => {
        indicator.remove();
    }, 1500);
}

// Initialize phone buttons
document.addEventListener('DOMContentLoaded', initPhoneButtons);

// Drag and Drop Functionality for App Icons
let draggedElement = null;
let dragOffset = { x: 0, y: 0 };

function initDragAndDrop() {
    const appItems = document.querySelectorAll('.app-item');

    appItems.forEach(item => {
        // Mouse events
        item.addEventListener('mousedown', startDrag);

        // Touch events for mobile
        item.addEventListener('touchstart', startDrag, { passive: false });
    });

    // Global mouse/touch events
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);
    document.addEventListener('touchmove', drag, { passive: false });
    document.addEventListener('touchend', endDrag);
}

function startDrag(e) {
    e.preventDefault();
    draggedElement = e.currentTarget;
    draggedElement.classList.add('dragging');

    const rect = draggedElement.getBoundingClientRect();
    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    const clientY = e.clientY || (e.touches && e.touches[0].clientY);

    dragOffset.x = clientX - rect.left;
    dragOffset.y = clientY - rect.top;

    // Bring to front
    draggedElement.style.zIndex = '1000';
}

function drag(e) {
    if (!draggedElement) return;

    e.preventDefault();

    const clientX = e.clientX || (e.touches && e.touches[0].clientX);
    const clientY = e.clientY || (e.touches && e.touches[0].clientY);

    const container = draggedElement.closest('.apps-page');
    const containerRect = container.getBoundingClientRect();

    let newX = clientX - containerRect.left - dragOffset.x;
    let newY = clientY - containerRect.top - dragOffset.y;

    // Constrain to container bounds
    const maxX = container.offsetWidth - draggedElement.offsetWidth;
    const maxY = container.offsetHeight - draggedElement.offsetHeight;

    newX = Math.max(0, Math.min(newX, maxX));
    newY = Math.max(0, Math.min(newY, maxY));

    draggedElement.style.left = newX + 'px';
    draggedElement.style.top = newY + 'px';
}

function endDrag(e) {
    if (!draggedElement) return;

    draggedElement.classList.remove('dragging');
    draggedElement.style.zIndex = '700';
    draggedElement = null;
}

// Initialize drag and drop when DOM is loaded
document.addEventListener('DOMContentLoaded', initDragAndDrop);

// Flashlight functionality
let isFlashlightOn = false;
let flashlightStream = null;

async function toggleFlashlight() {
    const flashlightBtn = document.getElementById('flashlightShortcut');
    const icon = flashlightBtn.querySelector('i');

    try {
        if (!isFlashlightOn) {
            // Turn on flashlight
            flashlightStream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' }
            });

            const track = flashlightStream.getVideoTracks()[0];
            const capabilities = track.getCapabilities();

            if (capabilities.torch) {
                await track.applyConstraints({
                    advanced: [{ torch: true }]
                });

                isFlashlightOn = true;
                icon.className = 'fas fa-lightbulb';
                flashlightBtn.style.background = 'linear-gradient(145deg, #FFD700 0%, #FFA500 100%)';
                flashlightBtn.style.color = '#000';
                flashlightBtn.style.border = '2px solid #FFD700';

                // Add glow effect
                flashlightBtn.style.boxShadow = `
                    0 0 20px rgba(255, 215, 0, 0.8),
                    0 0 40px rgba(255, 215, 0, 0.6),
                    0 0 60px rgba(255, 215, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3)
                `;

                // Add pulsing animation
                flashlightBtn.style.animation = 'flashlightPulse 2s ease-in-out infinite';

                console.log('Flashlight turned ON');
            } else {
                throw new Error('Torch not supported');
            }
        } else {
            // Turn off flashlight
            if (flashlightStream) {
                const track = flashlightStream.getVideoTracks()[0];
                await track.applyConstraints({
                    advanced: [{ torch: false }]
                });

                flashlightStream.getTracks().forEach(track => track.stop());
                flashlightStream = null;
            }

            isFlashlightOn = false;
            icon.className = 'fas fa-lightbulb';
            flashlightBtn.style.background = '';
            flashlightBtn.style.color = '';
            flashlightBtn.style.border = '';
            flashlightBtn.style.boxShadow = '';
            flashlightBtn.style.animation = '';

            console.log('Flashlight turned OFF');
        }
    } catch (error) {
        console.log('Flashlight not supported or permission denied:', error);

        // Fallback: Visual effect only
        if (!isFlashlightOn) {
            isFlashlightOn = true;
            icon.className = 'fas fa-lightbulb';
            flashlightBtn.style.background = 'linear-gradient(145deg, #FFD700 0%, #FFA500 100%)';
            flashlightBtn.style.color = '#000';
            flashlightBtn.style.border = '2px solid #FFD700';
            flashlightBtn.style.boxShadow = `
                0 0 20px rgba(255, 215, 0, 0.8),
                0 0 40px rgba(255, 215, 0, 0.6),
                inset 0 1px 2px rgba(255, 255, 255, 0.3)
            `;
            flashlightBtn.style.animation = 'flashlightPulse 2s ease-in-out infinite';

            // Make screen brighter
            document.body.style.filter = 'brightness(1.3)';
        } else {
            isFlashlightOn = false;
            icon.className = 'fas fa-lightbulb';
            flashlightBtn.style.background = '';
            flashlightBtn.style.color = '';
            flashlightBtn.style.border = '';
            flashlightBtn.style.boxShadow = '';
            flashlightBtn.style.animation = '';
            document.body.style.filter = '';
        }
    }
}

// Battery and Weather functionality

// Update Battery Display
function updateBattery() {
    // Update status bar battery
    const statusBatteryLevel = document.getElementById('statusBatteryLevel');
    const statusBatteryPercentage = document.getElementById('statusBatteryPercentage');

    // Update lock screen battery
    const lockBatteryLevel = document.getElementById('lockBatteryLevel');
    const lockBatteryPercentage = document.getElementById('lockBatteryPercentage');

    // Update status bar
    if (statusBatteryLevel && statusBatteryPercentage) {
        statusBatteryLevel.style.width = batteryLevel + '%';
        statusBatteryPercentage.textContent = batteryLevel + '%';

        // Update battery color based on level
        statusBatteryLevel.className = 'battery-level';
        if (batteryLevel <= 20) {
            statusBatteryLevel.classList.add('critical');
        } else if (batteryLevel <= 40) {
            statusBatteryLevel.classList.add('low');
        }
    }

    // Update lock screen
    if (lockBatteryLevel && lockBatteryPercentage) {
        lockBatteryLevel.style.width = batteryLevel + '%';
        lockBatteryPercentage.textContent = batteryLevel + '%';

        // Update battery color based on level
        lockBatteryLevel.className = 'battery-level';
        if (batteryLevel <= 20) {
            lockBatteryLevel.classList.add('critical');
        } else if (batteryLevel <= 40) {
            lockBatteryLevel.classList.add('low');
        }
    }
}

// Get Real Weather Data
function updateWeather() {
    const weatherIcon = document.getElementById('weatherIcon');
    const weatherTemp = document.getElementById('weatherTemp');
    const weatherLocation = document.getElementById('weatherLocation');

    try {
        // Get user's location
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(async (position) => {
                const lat = position.coords.latitude;
                const lon = position.coords.longitude;

                try {
                    // Using OpenWeatherMap API (free tier)
                    const API_KEY = 'b8d1c6c8f8c1c6c8f8c1c6c8f8c1c6c8'; // Demo key
                    const response = await fetch(`https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lon}&appid=${API_KEY}&units=metric&lang=ar`);

                    if (response.ok) {
                        const data = await response.json();
                        const temp = Math.round(data.main.temp);
                        const description = data.weather[0].main;
                        const city = data.name;

                        // Update weather display
                        if (weatherTemp) weatherTemp.textContent = `${temp}°`;
                        if (weatherLocation) weatherLocation.textContent = city;

                        // Update weather icon based on condition
                        updateWeatherIcon(description, weatherIcon);
                    } else {
                        throw new Error('Weather API failed');
                    }
                } catch (error) {
                    console.log('Weather API error:', error);
                    // Fallback to simulated weather
                    setFallbackWeather();
                }
            }, (error) => {
                console.log('Geolocation error:', error);
                setFallbackWeather();
            });
        } else {
            setFallbackWeather();
        }
    } catch (error) {
        console.log('Weather error:', error);
        setFallbackWeather();
    }
}

// Fallback weather data
function setFallbackWeather() {
    const weatherIcon = document.getElementById('weatherIcon');
    const weatherTemp = document.getElementById('weatherTemp');
    const weatherLocation = document.getElementById('weatherLocation');

    // Simulate weather based on time of day
    const hour = new Date().getHours();
    let temp, icon;

    if (hour >= 6 && hour < 18) {
        // Daytime
        temp = Math.floor(Math.random() * 15) + 20; // 20-35°C
        icon = 'fas fa-sun';
    } else {
        // Nighttime
        temp = Math.floor(Math.random() * 10) + 15; // 15-25°C
        icon = 'fas fa-moon';
    }

    if (weatherIcon) {
        weatherIcon.className = icon;
    }

    if (weatherTemp) {
        weatherTemp.textContent = `${temp}°`;
    }

    if (weatherLocation) {
        weatherLocation.textContent = 'بغداد';
    }
}

// Update weather icon based on condition
function updateWeatherIcon(condition, iconElement) {
    const iconMap = {
        'Clear': 'fas fa-sun',
        'Clouds': 'fas fa-cloud',
        'Rain': 'fas fa-cloud-rain',
        'Snow': 'fas fa-snowflake',
        'Thunderstorm': 'fas fa-bolt',
        'Drizzle': 'fas fa-cloud-drizzle',
        'Mist': 'fas fa-smog',
        'Fog': 'fas fa-smog'
    };

    iconElement.className = iconMap[condition] || 'fas fa-sun';
}

// Get real battery level (if supported)
function getRealBatteryLevel() {
    // Use simulated battery level for compatibility
    updateBattery();
}

// Initialize battery, weather and flashlight
document.addEventListener('DOMContentLoaded', function() {
    // Initialize battery
    updateBattery();

    // Initialize weather
    updateWeather();

    // Update weather every 10 minutes
    setInterval(updateWeather, 600000);

    // Initialize flashlight
    const flashlightBtn = document.getElementById('flashlightShortcut');
    if (flashlightBtn) {
        flashlightBtn.addEventListener('click', toggleFlashlight);
    }
});
